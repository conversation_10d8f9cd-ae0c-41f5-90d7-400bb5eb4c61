#!/usr/bin/env node

/**
 * 微信小程序功能测试脚本
 * 测试各个页面和组件的基本功能
 */

const fs = require('fs');
const path = require('path');

console.log('🚀 开始测试微信小程序功能...\n');

// 测试配置文件
function testConfigFiles() {
  console.log('📋 测试配置文件...');
  
  const configFiles = [
    'app.json',
    'project.config.json',
    'sitemap.json'
  ];
  
  configFiles.forEach(file => {
    try {
      const content = fs.readFileSync(file, 'utf8');
      JSON.parse(content);
      console.log(`✅ ${file} - 配置文件格式正确`);
    } catch (error) {
      console.log(`❌ ${file} - 配置文件错误: ${error.message}`);
    }
  });
  
  console.log('');
}

// 测试页面配置
function testPageConfigs() {
  console.log('📄 测试页面配置...');
  
  const pagesDir = 'pages';
  const pages = fs.readdirSync(pagesDir);
  
  pages.forEach(page => {
    const pagePath = path.join(pagesDir, page);
    if (fs.statSync(pagePath).isDirectory()) {
      const jsonFile = path.join(pagePath, 'index.json');
      if (fs.existsSync(jsonFile)) {
        try {
          const content = fs.readFileSync(jsonFile, 'utf8');
          JSON.parse(content);
          console.log(`✅ ${page} - 页面配置正确`);
        } catch (error) {
          console.log(`❌ ${page} - 页面配置错误: ${error.message}`);
        }
      }
    }
  });
  
  console.log('');
}

// 测试组件配置
function testComponentConfigs() {
  console.log('🧩 测试组件配置...');
  
  const componentsDir = 'components';
  if (fs.existsSync(componentsDir)) {
    const components = fs.readdirSync(componentsDir);
    
    components.forEach(component => {
      const componentPath = path.join(componentsDir, component);
      if (fs.statSync(componentPath).isDirectory()) {
        const jsonFile = path.join(componentPath, `${component}.json`);
        if (fs.existsSync(jsonFile)) {
          try {
            const content = fs.readFileSync(jsonFile, 'utf8');
            JSON.parse(content);
            console.log(`✅ ${component} - 组件配置正确`);
          } catch (error) {
            console.log(`❌ ${component} - 组件配置错误: ${error.message}`);
          }
        }
      }
    });
  }
  
  console.log('');
}

// 测试 Vant 组件路径
function testVantComponents() {
  console.log('🎨 测试 Vant 组件路径...');
  
  const vantPath = 'miniprogram_npm/@vant/weapp/lib';
  
  if (!fs.existsSync(vantPath)) {
    console.log('❌ Vant 组件目录不存在');
    return;
  }
  
  const requiredComponents = [
    'toast', 'loading', 'dialog', 'notify', 'overlay',
    'button', 'cell', 'cell-group', 'field', 'popup',
    'picker', 'datetime-picker', 'action-sheet', 'swipe-cell',
    'tag', 'empty', 'divider', 'icon'
  ];
  
  requiredComponents.forEach(component => {
    const componentPath = path.join(vantPath, component, 'index.js');
    if (fs.existsSync(componentPath)) {
      console.log(`✅ ${component} - 组件文件存在`);
    } else {
      console.log(`❌ ${component} - 组件文件不存在`);
    }
  });
  
  console.log('');
}

// 测试依赖包
function testDependencies() {
  console.log('📦 测试依赖包...');
  
  try {
    const packageJson = JSON.parse(fs.readFileSync('package.json', 'utf8'));
    const dependencies = packageJson.dependencies || {};
    
    Object.keys(dependencies).forEach(dep => {
      const depPath = path.join('node_modules', dep);
      if (fs.existsSync(depPath)) {
        console.log(`✅ ${dep} - 依赖包已安装`);
      } else {
        console.log(`❌ ${dep} - 依赖包未安装`);
      }
    });
  } catch (error) {
    console.log(`❌ 读取 package.json 失败: ${error.message}`);
  }
  
  console.log('');
}

// 运行所有测试
function runAllTests() {
  testConfigFiles();
  testPageConfigs();
  testComponentConfigs();
  testVantComponents();
  testDependencies();
  
  console.log('🎉 测试完成！');
  console.log('\n💡 提示：');
  console.log('1. 如果所有测试都通过，可以在微信开发者工具中打开项目');
  console.log('2. 在开发者工具中点击"工具" -> "构建 npm"');
  console.log('3. 然后可以正常预览和调试小程序');
}

// 执行测试
runAllTests();
