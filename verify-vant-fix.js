#!/usr/bin/env node

/**
 * 验证 Vant 组件修复的完整性
 */

const fs = require('fs');
const path = require('path');

console.log('🔍 验证 Vant 组件修复...\n');

// 检查 JavaScript 文件中的 import 语句
function checkJavaScriptImports() {
  console.log('📝 检查 JavaScript 文件中的 import 语句...');
  
  const jsFiles = [
    'utils/ui.js',
    'pages/today_order/index.js'
  ];
  
  let allCorrect = true;
  
  jsFiles.forEach(file => {
    if (fs.existsSync(file)) {
      const content = fs.readFileSync(file, 'utf8');
      
      // 检查是否有错误的 import 路径
      const wrongImports = content.match(/@vant\/weapp\/(?!lib\/)[^'"\s]+/g);
      if (wrongImports) {
        console.log(`❌ ${file} - 发现错误的 import 路径: ${wrongImports.join(', ')}`);
        allCorrect = false;
      } else {
        console.log(`✅ ${file} - import 路径正确`);
      }
    }
  });
  
  if (allCorrect) {
    console.log('✅ 所有 JavaScript import 路径正确');
  }
  
  console.log('');
  return allCorrect;
}

// 检查 JSON 配置文件中的组件路径
function checkJsonConfigs() {
  console.log('📋 检查 JSON 配置文件中的组件路径...');
  
  const configFiles = [
    'app.json',
    ...fs.readdirSync('pages').map(page => `pages/${page}/index.json`),
    ...fs.readdirSync('components').map(comp => `components/${comp}/${comp}.json`)
  ].filter(file => fs.existsSync(file));
  
  let allCorrect = true;
  
  configFiles.forEach(file => {
    try {
      const content = fs.readFileSync(file, 'utf8');
      const config = JSON.parse(content);
      
      if (config.usingComponents) {
        const wrongPaths = [];
        
        Object.entries(config.usingComponents).forEach(([name, path]) => {
          if (path.startsWith('@vant/weapp/') && !path.includes('/lib/')) {
            wrongPaths.push(`${name}: ${path}`);
          }
        });
        
        if (wrongPaths.length > 0) {
          console.log(`❌ ${file} - 发现错误的组件路径:`);
          wrongPaths.forEach(p => console.log(`   ${p}`));
          allCorrect = false;
        } else {
          const vantComponents = Object.entries(config.usingComponents)
            .filter(([name, path]) => path.startsWith('@vant/weapp/'))
            .length;
          
          if (vantComponents > 0) {
            console.log(`✅ ${file} - ${vantComponents} 个 Vant 组件路径正确`);
          } else {
            console.log(`✅ ${file} - 配置正确`);
          }
        }
      } else {
        console.log(`✅ ${file} - 无组件配置`);
      }
    } catch (error) {
      console.log(`❌ ${file} - 读取失败: ${error.message}`);
      allCorrect = false;
    }
  });
  
  console.log('');
  return allCorrect;
}

// 检查 Vant 组件文件是否存在
function checkVantFiles() {
  console.log('📦 检查 Vant 组件文件...');
  
  const vantLibPath = 'miniprogram_npm/@vant/weapp/lib';
  
  if (!fs.existsSync(vantLibPath)) {
    console.log('❌ Vant lib 目录不存在');
    return false;
  }
  
  const requiredComponents = [
    'toast', 'loading', 'dialog', 'notify', 'overlay',
    'button', 'cell', 'cell-group', 'field', 'popup',
    'picker', 'datetime-picker', 'action-sheet', 'swipe-cell',
    'tag', 'empty', 'divider', 'icon'
  ];
  
  let allExist = true;
  
  requiredComponents.forEach(component => {
    const componentPath = path.join(vantLibPath, component);
    const indexFile = path.join(componentPath, 'index.js');
    
    if (fs.existsSync(indexFile)) {
      console.log(`✅ ${component} - 组件文件存在`);
    } else {
      console.log(`❌ ${component} - 组件文件不存在`);
      allExist = false;
    }
  });
  
  // 检查特殊的 toast.js 文件
  const toastJsFile = path.join(vantLibPath, 'toast', 'toast.js');
  if (fs.existsSync(toastJsFile)) {
    console.log(`✅ toast.js - 特殊文件存在`);
  } else {
    console.log(`❌ toast.js - 特殊文件不存在`);
    allExist = false;
  }
  
  // 检查特殊的 dialog.js 文件
  const dialogJsFile = path.join(vantLibPath, 'dialog', 'dialog.js');
  if (fs.existsSync(dialogJsFile)) {
    console.log(`✅ dialog.js - 特殊文件存在`);
  } else {
    console.log(`❌ dialog.js - 特殊文件不存在`);
    allExist = false;
  }
  
  // 检查特殊的 notify.js 文件
  const notifyJsFile = path.join(vantLibPath, 'notify', 'notify.js');
  if (fs.existsSync(notifyJsFile)) {
    console.log(`✅ notify.js - 特殊文件存在`);
  } else {
    console.log(`❌ notify.js - 特殊文件不存在`);
    allExist = false;
  }
  
  console.log('');
  return allExist;
}

// 生成修复报告
function generateFixReport(jsImportsOk, jsonConfigsOk, vantFilesOk) {
  console.log('📊 生成修复报告...');
  
  const report = {
    timestamp: new Date().toISOString(),
    status: jsImportsOk && jsonConfigsOk && vantFilesOk ? 'SUCCESS' : 'FAILED',
    checks: {
      javascriptImports: jsImportsOk ? 'PASS' : 'FAIL',
      jsonConfigs: jsonConfigsOk ? 'PASS' : 'FAIL',
      vantFiles: vantFilesOk ? 'PASS' : 'FAIL'
    },
    summary: {
      totalIssuesFixed: 'JavaScript import 路径修复, JSON 配置路径修复, miniprogram_npm 目录创建',
      remainingIssues: jsImportsOk && jsonConfigsOk && vantFilesOk ? '无' : '存在问题需要进一步修复'
    },
    nextSteps: [
      '在微信开发者工具中打开项目',
      '点击"工具" -> "构建 npm"',
      '测试各个页面的 Vant 组件功能',
      '如果仍有问题，检查控制台错误信息'
    ]
  };
  
  fs.writeFileSync('vant-fix-report.json', JSON.stringify(report, null, 2));
  console.log('✅ 修复报告已生成: vant-fix-report.json');
  
  return report;
}

// 运行所有检查
function runAllChecks() {
  const jsImportsOk = checkJavaScriptImports();
  const jsonConfigsOk = checkJsonConfigs();
  const vantFilesOk = checkVantFiles();
  
  const report = generateFixReport(jsImportsOk, jsonConfigsOk, vantFilesOk);
  
  console.log('\n🎯 修复状态总结:');
  console.log(`JavaScript imports: ${report.checks.javascriptImports}`);
  console.log(`JSON 配置: ${report.checks.jsonConfigs}`);
  console.log(`Vant 文件: ${report.checks.vantFiles}`);
  console.log(`总体状态: ${report.status}`);
  
  if (report.status === 'SUCCESS') {
    console.log('\n🎉 所有 Vant 组件问题已修复！');
    console.log('现在可以在微信开发者工具中正常使用了。');
  } else {
    console.log('\n⚠️  仍有问题需要解决，请查看上面的详细信息。');
  }
}

// 执行检查
runAllChecks();
