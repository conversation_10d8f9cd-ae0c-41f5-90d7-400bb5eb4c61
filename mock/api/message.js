/**
 * 消息相关 API
 */

const { mockMessages } = require('../user');

// 消息数据
let messages = [...mockMessages];

// 处理消息相关请求
function handleRequest(req) {
  const { method, params, data } = req;
  const { id, subResource } = params;
  
  // 根据请求方法和路径处理不同的请求
  switch (method) {
    case 'GET':
      if (id) {
        // GET /messages/:id - 获取指定消息
        return getMessageById(id);
      } else {
        // GET /messages - 获取所有消息
        return getAllMessages();
      }
    
    case 'POST':
      // POST /messages - 创建新消息
      return createMessage(data);
      
    case 'PUT':
      if (id) {
        // PUT /messages/:id - 更新消息
        return updateMessage(id, data);
      }
      return { code: 400, message: 'Invalid request', data: null };
      
    case 'DELETE':
      if (id) {
        // DELETE /messages/:id - 删除消息
        return deleteMessage(id);
      }
      return { code: 400, message: 'Invalid request', data: null };
      
    default:
      return { code: 405, message: 'Method not allowed', data: null };
  }
}

// 获取所有消息
function getAllMessages() {
  return {
    code: 200,
    message: 'Success',
    data: messages
  };
}

// 获取指定消息
function getMessageById(id) {
  const message = messages.find(m => m.id == id);
  
  if (message) {
    return {
      code: 200,
      message: 'Success',
      data: message
    };
  }
  
  return {
    code: 404,
    message: 'Message not found',
    data: null
  };
}

// 创建新消息
function createMessage(data) {
  if (!data || !data.content || !data.user_id) {
    return {
      code: 400,
      message: 'Invalid message data',
      data: null
    };
  }
  
  const now = new Date();
  const formattedDate = `${now.getFullYear()}-${String(now.getMonth() + 1).padStart(2, '0')}-${String(now.getDate()).padStart(2, '0')} ${String(now.getHours()).padStart(2, '0')}:${String(now.getMinutes()).padStart(2, '0')}:${String(now.getSeconds()).padStart(2, '0')}`;
  
  const newMessage = {
    id: messages.length + 1,
    content: data.content,
    user_id: data.user_id,
    user_name: data.user_name || '用户',
    created_at: formattedDate,
    read: false
  };
  
  messages.unshift(newMessage);
  
  return {
    code: 201,
    message: 'Message created',
    data: newMessage
  };
}

// 更新消息
function updateMessage(id, data) {
  const index = messages.findIndex(m => m.id == id);
  
  if (index === -1) {
    return {
      code: 404,
      message: 'Message not found',
      data: null
    };
  }
  
  messages[index] = { ...messages[index], ...data };
  
  return {
    code: 200,
    message: 'Message updated',
    data: messages[index]
  };
}

// 删除消息
function deleteMessage(id) {
  const index = messages.findIndex(m => m.id == id);
  
  if (index === -1) {
    return {
      code: 404,
      message: 'Message not found',
      data: null
    };
  }
  
  const deletedMessage = messages[index];
  messages = messages.filter(m => m.id != id);
  
  return {
    code: 200,
    message: 'Message deleted',
    data: deletedMessage
  };
}

module.exports = {
  handleRequest
};
