/**
 * 用户相关 API
 */

const {mockUserInfo, mockFamilyMembers} = require('../user');

// 用户数据
let users = [mockUserInfo, ...mockFamilyMembers];

// 处理用户相关请求
function handleRequest(req) {
  const {method, params, data} = req;
  const {id, subResource} = params;

  // 根据请求方法和路径处理不同的请求
  switch (method) {
    case 'GET':
      if (id) {
        // GET /users/:id - 获取指定用户信息
        return getUserById(id);
      } else if (subResource === 'family') {
        // GET /users/family - 获取家庭成员列表
        return getFamilyMembers();
      } else {
        // GET /users - 获取所有用户
        return getAllUsers();
      }

    case 'POST':
      if (subResource === 'login') {
        // POST /users/login - 用户登录
        return login(data);
      } else {
        // POST /users - 创建新用户
        return createUser(data);
      }

    case 'PUT':
      if (id) {
        // PUT /users/:id - 更新用户信息
        return updateUser(id, data);
      }
      return {code: 400, message: 'Invalid request', data: null};

    case 'DELETE':
      if (id) {
        // DELETE /users/:id - 删除用户
        return deleteUser(id);
      }
      return {code: 400, message: 'Invalid request', data: null};

    default:
      return {code: 405, message: 'Method not allowed', data: null};
  }
}

// 获取所有用户
function getAllUsers() {
  return {
    code: 200,
    message: 'Success',
    data: users
  };
}

// 获取指定用户信息
function getUserById(id) {
  const user = users.find(u => u.id == id);

  if (user) {
    return {
      code: 200,
      message: 'Success',
      data: user
    };
  }

  return {
    code: 404,
    message: 'User not found',
    data: null
  };
}

// 获取家庭成员列表
function getFamilyMembers() {
  return {
    code: 200,
    message: 'Success',
    data: mockFamilyMembers
  };
}

// 用户登录
function login(data) {
  // 根据登录类型处理不同的登录方式
  if (data.loginType === 'wechat') {
    // 微信登录
    if (data.code && data.userInfo) {
      // 模拟微信登录成功
      const wechatUser = {
        id: 1,
        name: data.userInfo.nickName || '微信用户',
        avatar: data.userInfo.avatarUrl || '',
        phone: '',
        gender: data.userInfo.gender || 0,
        openid: 'mock_openid_' + Date.now()
      };

      return {
        code: 200,
        message: 'Wechat login successful',
        data: {
          token: 'mock_wechat_token_' + Date.now(),
          user: wechatUser
        }
      };
    }

    return {
      code: 401,
      message: 'Invalid wechat credentials',
      data: null
    };
  } else {
    // 账号密码登录
    if (data.username && data.password) {
      // 模拟手机号登录
      if (/^1\d{10}$/.test(data.username)) {
        return {
          code: 200,
          message: 'Phone login successful',
          data: {
            token: 'mock_phone_token_' + Date.now(),
            user: {
              ...mockUserInfo,
              phone: data.username
            }
          }
        };
      }

      // 模拟账号登录
      return {
        code: 200,
        message: 'Account login successful',
        data: {
          token: 'mock_account_token_' + Date.now(),
          user: mockUserInfo
        }
      };
    }

    return {
      code: 401,
      message: 'Invalid credentials',
      data: null
    };
  }
}

// 创建新用户
function createUser(data) {
  if (!data || !data.name) {
    return {
      code: 400,
      message: 'Invalid user data',
      data: null
    };
  }

  const newUser = {
    id: users.length + 1,
    name: data.name,
    avatar: data.avatar || '',
    role: data.role || 'user',
    created: new Date().toISOString().split('T')[0]
  };

  users.push(newUser);

  return {
    code: 201,
    message: 'User created',
    data: newUser
  };
}

// 更新用户信息
function updateUser(id, data) {
  const index = users.findIndex(u => u.id == id);

  if (index === -1) {
    return {
      code: 404,
      message: 'User not found',
      data: null
    };
  }

  users[index] = {...users[index], ...data};

  return {
    code: 200,
    message: 'User updated',
    data: users[index]
  };
}

// 删除用户
function deleteUser(id) {
  const index = users.findIndex(u => u.id == id);

  if (index === -1) {
    return {
      code: 404,
      message: 'User not found',
      data: null
    };
  }

  const deletedUser = users[index];
  users = users.filter(u => u.id != id);

  return {
    code: 200,
    message: 'User deleted',
    data: deletedUser
  };
}

module.exports = {
  handleRequest
};
