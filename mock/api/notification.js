/**
 * 通知相关 API
 */

// 通知数据
let notifications = [
  {
    id: 1,
    content: '今晚8点厨房消毒，请提前取餐！',
    created_at: '2025-04-29 10:00:00',
    user_id: 2,
    read: false
  },
  {
    id: 2,
    content: '本周六有家庭聚餐，欢迎大家提前点菜！',
    created_at: '2025-04-28 09:00:00',
    user_id: 3,
    read: true
  }
];

// 处理通知相关请求
function handleRequest(req) {
  const { method, params, data } = req;
  const { id, subResource } = params;
  
  // 根据请求方法和路径处理不同的请求
  switch (method) {
    case 'GET':
      if (id) {
        // GET /notifications/:id - 获取指定通知
        return getNotificationById(id);
      } else {
        // GET /notifications - 获取所有通知
        return getAllNotifications();
      }
    
    case 'POST':
      // POST /notifications - 创建新通知
      return createNotification(data);
      
    case 'PUT':
      if (id) {
        // PUT /notifications/:id - 更新通知
        return updateNotification(id, data);
      }
      return { code: 400, message: 'Invalid request', data: null };
      
    case 'DELETE':
      if (id) {
        // DELETE /notifications/:id - 删除通知
        return deleteNotification(id);
      }
      return { code: 400, message: 'Invalid request', data: null };
      
    default:
      return { code: 405, message: 'Method not allowed', data: null };
  }
}

// 获取所有通知
function getAllNotifications() {
  return {
    code: 200,
    message: 'Success',
    data: notifications
  };
}

// 获取指定通知
function getNotificationById(id) {
  const notification = notifications.find(n => n.id == id);
  
  if (notification) {
    return {
      code: 200,
      message: 'Success',
      data: notification
    };
  }
  
  return {
    code: 404,
    message: 'Notification not found',
    data: null
  };
}

// 创建新通知
function createNotification(data) {
  if (!data || !data.content) {
    return {
      code: 400,
      message: 'Invalid notification data',
      data: null
    };
  }
  
  const now = new Date();
  const formattedDate = `${now.getFullYear()}-${String(now.getMonth() + 1).padStart(2, '0')}-${String(now.getDate()).padStart(2, '0')} ${String(now.getHours()).padStart(2, '0')}:${String(now.getMinutes()).padStart(2, '0')}:${String(now.getSeconds()).padStart(2, '0')}`;
  
  const newNotification = {
    id: notifications.length + 1,
    content: data.content,
    created_at: formattedDate,
    user_id: data.user_id || 1,
    read: false
  };
  
  notifications.unshift(newNotification);
  
  return {
    code: 201,
    message: 'Notification created',
    data: newNotification
  };
}

// 更新通知
function updateNotification(id, data) {
  const index = notifications.findIndex(n => n.id == id);
  
  if (index === -1) {
    return {
      code: 404,
      message: 'Notification not found',
      data: null
    };
  }
  
  notifications[index] = { ...notifications[index], ...data };
  
  return {
    code: 200,
    message: 'Notification updated',
    data: notifications[index]
  };
}

// 删除通知
function deleteNotification(id) {
  const index = notifications.findIndex(n => n.id == id);
  
  if (index === -1) {
    return {
      code: 404,
      message: 'Notification not found',
      data: null
    };
  }
  
  const deletedNotification = notifications[index];
  notifications = notifications.filter(n => n.id != id);
  
  return {
    code: 200,
    message: 'Notification deleted',
    data: deletedNotification
  };
}

module.exports = {
  handleRequest
};
