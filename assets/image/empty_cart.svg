<?xml version="1.0" encoding="UTF-8"?>
<svg width="200px" height="200px" viewBox="0 0 200 200" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
    <title>Empty Cart</title>
    <defs>
        <linearGradient x1="0%" y1="0%" x2="100%" y2="100%" id="linearGradient-1">
            <stop stop-color="#FE2C55" offset="0%"></stop>
            <stop stop-color="#00F2EA" offset="100%"></stop>
        </linearGradient>
    </defs>
    <g id="Empty-Cart" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">
        <g id="Cart" transform="translate(30.000000, 40.000000)">
            <path d="M120,30 L100,90 L20,90 L0,30 L120,30 Z" id="Cart-Body" stroke="url(#linearGradient-1)" stroke-width="4" opacity="0.8"></path>
            <path d="M30,30 L30,0 C30,0 50,0 60,15 C70,0 90,0 90,0 L90,30" id="Cart-Handle" stroke="url(#linearGradient-1)" stroke-width="4" opacity="0.8"></path>
            <circle id="Wheel-1" fill="#00F2EA" cx="35" cy="105" r="10"></circle>
            <circle id="Wheel-2" fill="#FE2C55" cx="85" cy="105" r="10"></circle>
        </g>
        <text id="Empty" font-family="Arial-BoldMT, Arial" font-size="16" font-weight="bold" fill="#B3E0F7">
            <tspan x="75" y="170">EMPTY</tspan>
        </text>
    </g>
</svg>
