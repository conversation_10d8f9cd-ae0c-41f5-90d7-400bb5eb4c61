/* 全局动画样式 */

/* 淡入 */
@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

.animate-fadeIn {
  animation: fadeIn 0.5s ease-out;
}

/* 从下淡入 */
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(20rpx);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.animate-fadeInUp {
  animation: fadeInUp 0.5s ease-out;
}

/* 从左淡入 */
@keyframes fadeInLeft {
  from {
    opacity: 0;
    transform: translateX(-20rpx);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

.animate-fadeInLeft {
  animation: fadeInLeft 0.5s ease-out;
}

/* 从右淡入 */
@keyframes fadeInRight {
  from {
    opacity: 0;
    transform: translateX(20rpx);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

.animate-fadeInRight {
  animation: fadeInRight 0.5s ease-out;
}

/* 缩放淡入 */
@keyframes zoomIn {
  from {
    opacity: 0;
    transform: scale(0.95);
  }
  to {
    opacity: 1;
    transform: scale(1);
  }
}

.animate-zoomIn {
  animation: zoomIn 0.5s ease-out;
}

/* 脉冲效果 */
@keyframes pulse {
  0% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.05);
  }
  100% {
    transform: scale(1);
  }
}

.animate-pulse {
  animation: pulse 1.5s infinite;
}

/* 闪烁效果 */
@keyframes flash {
  0%, 50%, 100% {
    opacity: 1;
  }
  25%, 75% {
    opacity: 0.5;
  }
}

.animate-flash {
  animation: flash 2s infinite;
}

/* 摇晃效果 */
@keyframes shake {
  0%, 100% {
    transform: translateX(0);
  }
  10%, 30%, 50%, 70%, 90% {
    transform: translateX(-5rpx);
  }
  20%, 40%, 60%, 80% {
    transform: translateX(5rpx);
  }
}

.animate-shake {
  animation: shake 0.8s ease-in-out;
}

/* 弹跳效果 */
@keyframes bounce {
  0%, 20%, 50%, 80%, 100% {
    transform: translateY(0);
  }
  40% {
    transform: translateY(-20rpx);
  }
  60% {
    transform: translateY(-10rpx);
  }
}

.animate-bounce {
  animation: bounce 1s ease-in-out;
}

/* 旋转效果 */
@keyframes rotate {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

.animate-rotate {
  animation: rotate 2s linear infinite;
}

/* 动画延迟类 */
.delay-100 {
  animation-delay: 0.1s;
}

.delay-200 {
  animation-delay: 0.2s;
}

.delay-300 {
  animation-delay: 0.3s;
}

.delay-400 {
  animation-delay: 0.4s;
}

.delay-500 {
  animation-delay: 0.5s;
}

/* 动画持续时间类 */
.duration-300 {
  animation-duration: 0.3s;
}

.duration-500 {
  animation-duration: 0.5s;
}

.duration-700 {
  animation-duration: 0.7s;
}

.duration-1000 {
  animation-duration: 1s;
}

.duration-1500 {
  animation-duration: 1.5s;
}

/* 动画填充模式 */
.fill-forwards {
  animation-fill-mode: forwards;
}

.fill-backwards {
  animation-fill-mode: backwards;
}

.fill-both {
  animation-fill-mode: both;
}
