const {
  menuApi,
  messageApi,
  notificationApi,
  userApi
} = require('../../services/api');

Page({
  data: {
    menu: [],
    showRecommended: false,
    messages: [],
    statistics: {
      todayDishes: 0,
      weeklyFavorite: '',
      totalOrders: 0,
      monthlyVisits: 0
    },
    userInfo: {
      name: '家庭厨房',
      avatar: 'https://picsum.photos/200/200'
    },
    notice: {
      text: '加载中...',
    },
    loading: true
  },

  onLoad() {
    if (!wx.getStorageSync('token')) {
      wx.reLaunch({
        url: '/pages/login/index'
      });
      return;
    }
    // 加载所有数据
    this.loadAllData();
  },

  onShow() {
    // 刷新菜单数据
    this.loadMenuData();
  },

  // 加载所有数据
  async loadAllData() {
    try {
      // 并行加载多个数据
      await Promise.all([
        this.loadMenuData(),
        this.loadMessagesData(),
        this.loadNoticeData(),
        this.loadUserData(),
        this.loadStatisticsData()
      ]);

      // 所有数据加载完成
      this.setData({
        loading: false,
        loadComplete: true
      });
    } catch (error) {
      console.error('加载数据失败:', error);
      wx.showToast({
        title: '加载数据失败',
        icon: 'none'
      });

      this.setData({
        loading: false,
        loadComplete: true
      });
    }
  },

  // 加载菜单数据
  async loadMenuData() {
    try {
      // 先尝试获取今日菜单
      const res = await menuApi.getTodayMenu();

      if (res.data && res.data.dishes && res.data.dishes.length > 0) {
        // 格式化菜单数据
        const formattedMenu = res.data.dishes.map(dish => {
          return {
            id: dish.id,
            name: dish.name,
            count: dish.count,
            image: dish.image ||
              'https://images.pexels.com/photos/2097090/pexels-photo-2097090.jpeg?auto=compress'
          };
        });

        this.setData({
          menu: formattedMenu,
          todayMenuDate: res.data.date,
          showRecommended: false
        });
      } else {
        // 如果没有今日菜单，获取推荐菜单
        const recRes = await menuApi.getRecommendedMenu();

        this.setData({
          menu: recRes.data,
          showRecommended: true
        });
      }
    } catch (error) {
      console.error('加载菜单数据失败:', error);
      // 加载失败时显示空菜单
      this.setData({
        menu: [],
        showRecommended: true
      });
    }
  },

  // 加载消息数据
  async loadMessagesData() {
    try {
      // 请求最新的6条家庭留言数据
      const res = await messageApi.getMessages({
        limit: 6,
        page: 1
      });

      if (res.code === 200) {
        // 处理不同的数据格式
        let messageData = res.data;

        // 确保数据是数组格式
        if (!Array.isArray(messageData)) {
          messageData =
            messageData.messages || messageData.list || messageData.data || [];
        }

        // 如果最终还不是数组，设为空数组
        if (!Array.isArray(messageData)) {
          messageData = [];
        }

        if (messageData.length > 0) {
          // 格式化消息数据，只取最新的6条
          const formattedMessages = messageData.slice(0, 6).map(msg => {
            const userName = msg.user?.name || msg.user_name || '用户';
            const content = msg.content || '暂无内容';
            return `${userName}：${content}`;
          });

          this.setData({
            messages: formattedMessages
          });
        } else {
          // 如果没有数据，显示默认消息
          this.setData({
            messages: [
              '欢迎使用家庭点餐系统',
              '快来给家人留言吧～',
              '今天想吃什么呢？'
            ]
          });
        }
      } else {
        throw new Error(res.message || '获取消息失败');
      }
    } catch (error) {
      console.error('加载消息数据失败:', error);
      // 加载失败时显示默认消息
      this.setData({
        messages: [
          '欢迎使用家庭点餐系统',
          '快来给家人留言吧～',
          '今天想吃什么呢？'
        ]
      });
    }
  },

  // 加载通知数据
  async loadNoticeData() {
    try {
      const res = await notificationApi.getNotifications();
      let text = '--暂无通知--'
      if (res.data && res.data.length > 0) {
        // 获取最新的通知
        const latestNotice = res.data[0];
        text = latestNotice.content;
      }
      this.setData({
        notice: {
          text
        }
      });
    } catch (error) {
      console.error('加载通知数据失败:', error);
    }
  },

  // 加载用户数据
  async loadUserData() {
    try {
      // 获取当前用户ID
      const userId = wx.getStorageSync('userId') || 1;
      const res = await userApi.getUserInfo(userId);

      if (res.data) {
        this.setData({
          userInfo: {
            name: res.data.name,
            avatar: res.data.avatar || 'https://picsum.photos/200/200'
          }
        });
      }
    } catch (error) {
      console.error('加载用户数据失败:', error);
    }
  },

  // 加载统计数据
  async loadStatisticsData() {
    try {
      const res = await menuApi.getStatistics();

      if (res.data) {
        this.setData({
          statistics: res.data
        });
      }
    } catch (error) {
      console.error('加载统计数据失败:', error);
      // 使用默认数据
      this.setData({
        statistics: {
          todayDishes: 0,
          weeklyFavorite: '暂无数据',
          totalOrders: 0,
          monthlyVisits: 0
        }
      });
    }
  },

  // 跳转到点菜页面
  goToOrder() {
    wx.switchTab({
      url: '/pages/order/index',
      success: () => {
        console.log('成功跳转到点菜页面');
      },
      fail: error => {
        console.error('跳转失败:', error);
        // 如果switchTab失败（可能order不是tabBar页面），尝试使用navigateTo
        wx.navigateTo({
          url: '/pages/order/index'
        });
      }
    });
  },

  // 跳转到菜品详情页
  async goToFoodDetail(e) {
    const {
      id
    } = e.currentTarget.dataset;

    try {
      wx.showLoading({
        title: '加载中...'
      });

      const {
        dishApi
      } = require('../../services/api');
      const result = await dishApi.getDishDetail(id);

      if (result.code === 200) {
        // 将详情数据存入缓存
        wx.setStorageSync('detailData', result.data);

        // 跳转到详情页
        wx.navigateTo({
          url: '/pages/detail/index'
        });
      } else {
        wx.showToast({
          title: '获取详情失败',
          icon: 'error'
        });
      }
    } catch (error) {
      console.error('获取菜品详情失败:', error);
      wx.showToast({
        title: '网络错误',
        icon: 'error'
      });
    } finally {
      wx.hideLoading();
    }
  },

  // 跳转到留言页面
  goToMessage() {
    wx.navigateTo({
      url: '/pages/family_message/index',
      success: () => {
        console.log('成功跳转到家庭留言页面');
      },
      fail: error => {
        console.error('跳转失败:', error);
      }
    });
  }
});