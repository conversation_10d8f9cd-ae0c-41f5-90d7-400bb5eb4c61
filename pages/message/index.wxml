<view class="container">
  <!-- 页面标题 -->
  <view class="page-header">
    <view class="page-title">通知中心</view>
  </view>

  <!-- 通知中心 -->
  <view class="main-card">
    <view wx:for="{{notices}}" wx:key="id" class="notice-item">
      <view class="notice-title">{{item.content}}</view>
      <view class="notice-time">{{item.time}}</view>
    </view>

    <view class="notice-form">
      <input
        class="notice-input"
        placeholder="新增通知内容（点击右侧发送）"
        placeholder-class="placeholder-style"
        maxlength="40"
        bindinput="onNoticeInput"
        value="{{noticeInput}}"
        confirm-type="send"
        bindconfirm="addNotice"
      />
      <view class="notice-send" bindtap="addNotice">
        <van-icon name="guide-o" class="send-icon" />
      </view>
    </view>
  </view>
</view>
