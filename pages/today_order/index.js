import Toast from '@vant/weapp/toast/toast';
const {orderApi, userApi} = require('../../services/api');

Page({
  data: {
    basketItems: [],
    showDialog: false,
    showUserSelector: false,
    emptyCartImage: '/assets/image/empty_cart.svg', // 使用 SVG 图片
    defaultFoodImage:
      'https://cdn.pixabay.com/photo/2017/05/07/08/56/food-2290814_1280.jpg',
    orderSubmitted: false,
    remark: '',
    selectedTime: '',
    selectedUser: null,
    familyMembers: [],
    timeArray: [
      ['今天', '明天', '后天'],
      ['早餐', '午餐', '晚餐'],
      [
        '06:00',
        '07:00',
        '08:00',
        '09:00',
        '10:00',
        '11:00',
        '12:00',
        '13:00',
        '14:00',
        '15:00',
        '16:00',
        '17:00',
        '18:00',
        '19:00',
        '20:00',
        '21:00'
      ]
    ],
    timeIndex: [0, 2, 10] // 默认选择今天晚餐18:00
  },

  onLoad(options) {
    // 页面加载时的逻辑
    this.initTimeSelector();
    this.loadFamilyMembers();
  },

  onShow() {
    // 从缓存中获取购物篮数据
    this.loadBasketData();

    // 获取订单提交状态
    const orderSubmitted = wx.getStorageSync('orderSubmitted') || false;
    this.setData({orderSubmitted});
  },

  // 加载家庭成员列表
  async loadFamilyMembers() {
    try {
      const result = await userApi.getFamilyMembers();
      if (result.code === 200) {
        const currentUser = wx.getStorageSync('userInfo');
        const familyMembers = result.data || [];

        // 设置当前用户为默认选择
        const selectedUser =
          familyMembers.find(member => member.id === currentUser.id) ||
          familyMembers[0];

        this.setData({
          familyMembers,
          selectedUser
        });
      }
    } catch (error) {
      console.error('加载家庭成员失败:', error);
      // 使用当前用户作为默认选择
      const currentUser = wx.getStorageSync('userInfo');
      this.setData({
        familyMembers: [currentUser],
        selectedUser: currentUser
      });
    }
  },

  // 初始化时间选择器
  initTimeSelector() {
    const now = new Date();
    const hour = now.getHours();
    let timeIndex = this.data.timeIndex;

    // 根据当前时间设置默认选项
    if (hour < 10) {
      timeIndex = [0, 0, hour - 6 >= 0 ? hour - 6 : 0]; // 早餐
    } else if (hour < 16) {
      timeIndex = [0, 1, hour - 6 >= 0 ? hour - 6 : 0]; // 午餐
    } else {
      timeIndex = [0, 2, hour - 6 >= 0 ? hour - 6 : 0]; // 晚餐
    }

    // 更新默认选择的时间
    this.setData({timeIndex});
    this.updateSelectedTime(timeIndex);
  },

  // 更新选择的时间
  updateSelectedTime(timeIndex) {
    const {timeArray} = this.data;
    const selectedTime = `${timeArray[0][timeIndex[0]]} ${
      timeArray[1][timeIndex[1]]
    } ${timeArray[2][timeIndex[2]]}`;
    this.setData({selectedTime});
  },

  // 时间选择器变化事件
  bindTimeChange(e) {
    const timeIndex = e.detail.value;
    this.setData({timeIndex});
    this.updateSelectedTime(timeIndex);
  },

  // 备注输入事件
  onRemarkInput(e) {
    this.setData({
      remark: e.detail.value
    });
  },

  // 加载购物篮数据
  loadBasketData() {
    const basket = wx.getStorageSync('basket') || {};
    const basketItems = Object.values(basket);

    this.setData({basketItems});
  },

  // 删除菜品
  deleteItem(e) {
    const id = e.currentTarget.dataset.id;
    let basket = wx.getStorageSync('basket') || {};

    if (basket[id]) {
      delete basket[id];
      wx.setStorageSync('basket', basket);
      this.loadBasketData();

      // 提示用户
      Toast.success('已删除');
    }
  },

  // 跳转到点菜页面
  goToOrder() {
    wx.switchTab({
      url: '/pages/order/index'
    });
  },

  // 跳转到历史菜单页面
  goToHistory() {
    wx.navigateTo({
      url: '/pages/history_menu/index'
    });
  },

  // 显示用户选择器
  showUserSelector() {
    this.setData({
      showUserSelector: true
    });
  },

  // 隐藏用户选择器
  hideUserSelector() {
    this.setData({
      showUserSelector: false
    });
  },

  // 选择用户
  selectUser(e) {
    const userId = e.currentTarget.dataset.userId;
    const selectedUser = this.data.familyMembers.find(
      member => member.id === userId
    );

    this.setData({
      selectedUser,
      showUserSelector: false
    });
  },

  // 提交订单
  submitOrder() {
    if (this.data.basketItems.length === 0) {
      Toast.fail('菜单为空');
      return;
    }

    if (!this.data.selectedUser) {
      Toast.fail('请选择推送人员');
      return;
    }

    this.setData({
      showDialog: true
    });
  },

  // 确认提交订单
  async handleConfirm() {
    const {basketItems, remark, selectedTime, selectedUser} = this.data;

    try {
      wx.showLoading({title: '推送中...'});

      // 构建订单数据
      const orderData = {
        items: basketItems.map(item => ({
          dishId: item.id,
          dishName: item.name,
          count: item.count
        })),
        remark: remark || '',
        diningTime: this.formatDiningTime(selectedTime),
        userId: selectedUser.id,
        userName: selectedUser.name
      };

      // 调用API创建订单
      const result = await orderApi.createOrder(orderData);

      if (result.code === 200) {
        // 构建历史菜单数据（本地缓存）
        const todayMenu = {
          id: result.data.id,
          date: selectedTime || '今日',
          dishes: basketItems,
          remark: remark,
          createdAt: new Date().toISOString(),
          status: 'pending'
        };

        // 获取历史菜单列表
        let historyMenus = wx.getStorageSync('historyMenus') || [];
        historyMenus.unshift(todayMenu);

        // 最多保存10条历史记录
        if (historyMenus.length > 10) {
          historyMenus = historyMenus.slice(0, 10);
        }

        // 保存历史菜单和今日菜单
        wx.setStorageSync('historyMenus', historyMenus);
        wx.setStorageSync('todayMenu', todayMenu);

        // 清空购物篮
        wx.setStorageSync('basket', {});

        // 设置订单已提交状态
        wx.setStorageSync('orderSubmitted', true);

        // 关闭对话框
        this.setData({
          showDialog: false,
          basketItems: [],
          orderSubmitted: true
        });

        // 发送推送通知
        await this.sendPushNotification(selectedUser, todayMenu);

        // 显示推送成功提示
        Toast.success({
          message: '菜单推送成功',
          forbidClick: true,
          duration: 1500
        });
      } else {
        throw new Error(result.message || '提交失败');
      }
    } catch (error) {
      console.error('提交订单失败:', error);
      Toast.fail(error.message || '提交失败，请重试');
    } finally {
      wx.hideLoading();
    }
  },

  // 发送推送通知
  async sendPushNotification(user, menu) {
    try {
      const {notificationApi} = require('../../services/api');

      // 构建推送消息
      const message = {
        title: '今日菜单推送',
        content: `${user.name}，今日菜单已为您准备好：${menu.dishes
          .map(d => d.name)
          .join('、')}`,
        type: 'menu_push',
        targetUserId: user.id,
        data: {
          menuId: menu.id,
          dishes: menu.dishes,
          diningTime: menu.date,
          remark: menu.remark
        }
      };

      // 发送推送通知
      const result = await notificationApi.createNotification(message);

      if (result.code === 200) {
        console.log('推送通知发送成功');
      } else {
        console.warn('推送通知发送失败:', result.message);
      }
    } catch (error) {
      console.error('发送推送通知失败:', error);
      // 推送失败不影响主流程
    }
  },

  // 格式化用餐时间
  formatDiningTime(selectedTime) {
    if (!selectedTime) {
      return new Date().toISOString();
    }

    const now = new Date();
    const today = new Date(now.getFullYear(), now.getMonth(), now.getDate());

    // 解析选择的时间
    const parts = selectedTime.split(' ');
    const dayOffset = parts[0] === '今天' ? 0 : parts[0] === '明天' ? 1 : 2;
    const timeStr = parts[2] || '18:00';
    const [hour, minute] = timeStr.split(':').map(Number);

    const diningDate = new Date(today);
    diningDate.setDate(diningDate.getDate() + dayOffset);
    diningDate.setHours(hour, minute, 0, 0);

    return diningDate.toISOString();
  },

  // 查看今日菜单
  viewTodayMenu() {
    wx.navigateTo({
      url: '/pages/history_menu/index?viewToday=true'
    });
  },

  // 继续点菜
  continueOrder() {
    // 重置订单已提交状态
    wx.setStorageSync('orderSubmitted', false);

    this.setData({
      orderSubmitted: false
    });

    // 跳转到点菜页面
    wx.switchTab({
      url: '/pages/order/index'
    });
  },

  // 取消提交订单
  handleCancel() {
    this.setData({
      showDialog: false
    });
  }
});
