const {dishApi} = require('../../services/api');

Page({
  data: {
    name: '',
    tempImagePath: '',
    remark: '',
    material: '',
    method: '',
    category: 'hot', // 默认分类
    categoryIndex: 0, // 默认选中第一个
    categoryLabel: '热菜', // 默认显示标签
    categories: [
      {value: 'hot', label: '热菜'},
      {value: 'cold', label: '凉菜'},
      {value: 'soup', label: '汤品'},
      {value: 'staple', label: '主食'},
      {value: 'dessert', label: '甜品'}
    ],
    loading: false
  },

  onLoad() {
    // 页面加载时的逻辑
  },

  // 输入菜品名称
  onNameInput(e) {
    this.setData({
      name: e.detail.value
    });
  },

  // 输入备注
  onRemarkInput(e) {
    this.setData({
      remark: e.detail.value
    });
  },

  // 输入原材料
  onMaterialInput(e) {
    this.setData({
      material: e.detail.value
    });
  },

  // 输入做法
  onMethodInput(e) {
    this.setData({
      method: e.detail.value
    });
  },

  // 选择分类
  onCategoryChange(e) {
    const index = e.detail.value;
    const selectedCategory = this.data.categories[index];
    this.setData({
      categoryIndex: index,
      category: selectedCategory.value,
      categoryLabel: selectedCategory.label
    });
  },

  // 获取分类中文名称
  getCategoryName(categoryValue) {
    const categoryMap = {
      hot: '热菜',
      cold: '凉菜',
      soup: '汤品',
      staple: '主食',
      dessert: '甜品'
    };
    return categoryMap[categoryValue] || '热菜';
  },

  // 选择图片
  chooseImage() {
    wx.chooseMedia({
      count: 1,
      mediaType: ['image'],
      sourceType: ['album', 'camera'],
      success: res => {
        const tempFilePath = res.tempFiles[0].tempFilePath;
        this.setData({
          tempImagePath: tempFilePath
        });
      }
    });
  },

  // 提交表单
  async submitForm() {
    const {name, tempImagePath, remark, material, method, category, loading} =
      this.data;

    // 防止重复提交
    if (loading) return;

    // 验证必填字段
    if (!name.trim()) {
      wx.showToast({
        title: '请输入菜品名称',
        icon: 'none'
      });
      return;
    }

    if (!material.trim()) {
      wx.showToast({
        title: '请输入原材料',
        icon: 'none'
      });
      return;
    }

    if (!method.trim()) {
      wx.showToast({
        title: '请输入制作方法',
        icon: 'none'
      });
      return;
    }

    this.setData({loading: true});

    try {
      wx.showLoading({title: '提交中...'});

      // 构建菜品数据
      const dishData = {
        name: name.trim(),
        description: `${material.trim()}\n\n制作方法：\n${method.trim()}`,
        category: this.getCategoryName(category),
        image:
          tempImagePath ||
          'https://images.pexels.com/photos/2097090/pexels-photo-2097090.jpeg?auto=compress'
      };

      // 调用API创建菜品
      const result = await dishApi.createDish(dishData);

      if (result.code === 200) {
        wx.showToast({
          title: '添加成功',
          icon: 'success',
          duration: 2000
        });

        // 清空表单
        this.setData({
          name: '',
          tempImagePath: '',
          remark: '',
          material: '',
          method: '',
          category: 'hot'
        });

        // 延迟跳转到点菜页面
        setTimeout(() => {
          wx.switchTab({
            url: '/pages/order/index'
          });
        }, 2000);
      } else {
        throw new Error(result.message || '添加失败');
      }
    } catch (error) {
      console.error('添加菜品失败:', error);
      wx.showToast({
        title: error.message || '添加失败，请重试',
        icon: 'none',
        duration: 2000
      });
    } finally {
      wx.hideLoading();
      this.setData({loading: false});
    }
  }
});
