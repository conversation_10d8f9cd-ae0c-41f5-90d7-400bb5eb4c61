<view class="container">
  <!-- 页面标题 -->
  <view class="card-header">
    <van-icon name="plus" size="40rpx" color="var(--primary-600)" />
    <text class="ml-2">新增菜品</text>
  </view>

  <!-- 表单卡片 -->
  <view class="card">
    <view class="input-group">
      <view class="input-label">菜品名称</view>
      <input
        class="input"
        placeholder="请输入菜品名称，如红烧肉"
        bindinput="onNameInput"
        value="{{name}}"
      />
    </view>

    <view class="input-group">
      <view class="input-label">菜品图片</view>
      <view class="flex items-center">
        <view class="btn btn-secondary mr-3" bindtap="chooseImage">
          <van-icon name="photo-o" size="32rpx" />
          <text class="ml-1">选择图片</text>
        </view>
        <text
          class="text-sm text-gray-500"
          >{{tempImagePath ? '已选择图片' : '可选择菜品图片'}}</text
        >
      </view>
      <image
        wx:if="{{tempImagePath}}"
        class="w-full h-48 rounded-lg mt-2"
        src="{{tempImagePath}}"
        mode="aspectFill"
      />
    </view>

    <view class="input-container">
      <view class="picker-label">
        <van-icon name="apps-o" class="icon-margin" />
        菜品分类
      </view>
      <picker
        class="picker-warm"
        bindchange="onCategoryChange"
        value="{{categoryIndex}}"
        range="{{categories}}"
        range-key="label"
      >
        <view class="picker-text">
          <text>{{categoryLabel}}</text>
          <van-icon name="arrow-down" class="arrow-icon" />
        </view>
      </picker>
    </view>

    <view class="input-container">
      <view class="picker-label">
        <van-icon name="edit" class="icon-margin" />
        备注说明
      </view>
      <textarea
        class="input-warm textarea-style"
        placeholder="备注（如口味、适合人群等，可选）"
        placeholder-class="placeholder-style"
        bindinput="onRemarkInput"
        value="{{remark}}"
        auto-height
        maxlength="200"
        show-confirm-bar="{{false}}"
      ></textarea>
    </view>

    <view class="input-container">
      <view class="picker-label">
        <van-icon name="shopping-cart-o" class="icon-margin" />
        原材料
      </view>
      <textarea
        class="input-warm textarea-style"
        placeholder="原材料（如五花肉、酱油、糖等）"
        placeholder-class="placeholder-style"
        bindinput="onMaterialInput"
        value="{{material}}"
        auto-height
        maxlength="200"
        show-confirm-bar="{{false}}"
      ></textarea>
    </view>

    <view class="input-container">
      <view class="picker-label">
        <van-icon name="notes-o" class="icon-margin" />
        制作方法
      </view>
      <textarea
        class="input-warm textarea-style"
        placeholder="制作方法（如步骤说明）"
        placeholder-class="placeholder-style"
        bindinput="onMethodInput"
        value="{{method}}"
        auto-height
        maxlength="500"
        show-confirm-bar="{{false}}"
      ></textarea>
    </view>

    <button
      class="submit-btn {{loading ? 'loading' : ''}}"
      bindtap="submitForm"
      disabled="{{loading}}"
    >
      <van-icon wx:if="{{loading}}" name="loading" class="icon-margin" />
      <van-icon wx:else name="plus" class="icon-margin" />
      {{loading ? '提交中...' : '新增菜品'}}
    </button>
  </view>
</view>
