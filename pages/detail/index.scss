/* 现代化设计 */
@import "../../styles/miniprogram-design.scss";

/* 菜品详情页面 - Tailwind CSS 风格 */

.detail-container {
  background-color: #f9fafb;
  color: #111827;
  min-height: 100vh;
  padding: $space-4;
}

.detail-card {
  background: linear-gradient(135deg, white, #f3f4f6);
  @include rounded-lg;
  @include shadow-md;
  margin: 32rpx auto 0 auto;
  padding: 48rpx 32rpx;
  border: 2rpx solid #4b5563;
  max-width: 600rpx;
  position: relative;
  overflow: hidden;
}

.detail-img {
  width: 200rpx;
  height: 200rpx;
  @include rounded-lg;
  object-fit: cover;
  @include bg-white;
  border: 4rpx solid $primary-solid;
  @include shadow-md;
  display: block;
  margin: 0 auto 24rpx auto;
}

/* 详情内容 */
.detail-title {
  font-size: 36rpx;
  font-weight: 700;
  color: $primary-solid;
  text-align: center;
  @include mb-2;
}

.detail-remark {
  color: #d1d5db;
  font-size: 26rpx;
  text-align: center;
  @include mb-4;
}

.detail-section {
  @include mb-3;
}

.detail-label {
  @include text-primary;
  font-weight: 600;
  margin-bottom: 12rpx;
  @include flex;
  @include items-center;
  font-size: 28rpx;
  gap: 8rpx;
}

.icon-margin {
  margin-right: 8rpx;
}

.detail-content {
  color: #f3f4f6;
  font-size: 26rpx;
  line-height: 1.6;
  @include bg-white;
  @include rounded-sm;
  padding: 20rpx;
  margin-bottom: 8rpx;
  word-break: break-all;
  border: 1rpx solid #374151;
}

/* 返回按钮 */
.back-btn {
  @include flex;
  @include items-center;
  @include justify-center;
  margin: 32rpx auto 0 auto;
  @include modern-btn;
  @include btn-primary;
  color: #111827;
  font-weight: 600;
  padding: 16rpx;
  @include rounded-md;
  font-size: 28rpx;
  @include shadow-md;
  width: 160rpx;
  transition: all 0.2s ease;

  &:active {
    transform: scale(0.95);
    @include shadow-md;
  }
}