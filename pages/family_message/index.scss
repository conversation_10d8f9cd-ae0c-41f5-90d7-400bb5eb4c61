/* 家庭留言页面 - 现代化设计 */
@import "../../styles/miniprogram-design.scss";

.container {
  @include page-container;
  @include page-container-safe;
}

.page-header {
  @include flex;
  @include items-center;
  @include justify-center;
  @include mb-4;
}

.page-title {
  @include text-2xl;
  @include font-bold;
  @include text-primary;
  @include text-center;
}

.main-card {
  @include modern-card;
  @include card-primary;
  @include mb-4;
  padding: 40rpx 32rpx;
}

.msg-item {
  @include modern-card;
  @include card-flat;
  @include mb-3;
  padding: 24rpx;
  @include transition;

  &:hover {
    transform: translateY(-2rpx);
    @include shadow-md;
  }
}

.msg-header {
  @include flex;
  @include items-center;
  @include mb-2;
}

.msg-user {
  @include font-bold;
  margin-right: 12rpx;
  @include text-base;

  &.blue {
    @include text-primary;
  }

  &.pink {
    @include text-secondary;
  }
}

.msg-content {
  @include text-gray-900;
  @include text-base;
  line-height: 1.6;
}

.msg-time {
  @include text-gray-500;
  @include text-xs;
  margin-top: 8rpx;
}

.msg-form {
  @include flex;
  @include gap-2;
  margin-top: 24rpx;
}

.msg-input {
  @include flex-1;
  @include modern-input;
  border-radius: 24rpx 0 0 24rpx;
  height: 88rpx;
  line-height: 88rpx;
}

.placeholder-style {
  @include text-gray-400;
  @include text-sm;
}

.msg-send {
  @include modern-btn;
  @include btn-primary;
  @include flex;
  @include items-center;
  @include justify-center;
  width: 100rpx;
  height: 88rpx;
  border-radius: 0 24rpx 24rpx 0;
  @include text-lg;
}

.send-icon {
  @include text-white;
  @include font-bold;
  font-size: 32rpx;
}
