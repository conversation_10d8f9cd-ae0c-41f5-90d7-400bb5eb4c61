<view class="container">
  <!-- 页面标题 -->
  <view class="page-header">
    <view class="page-title">家庭留言</view>
  </view>

  <!-- 家庭留言 -->
  <view class="main-card">
    <view wx:for="{{messages}}" wx:key="id" class="msg-item">
      <view class="msg-header">
        <text class="msg-user {{item.userType}}">{{item.userName}}</text>
        <text class="msg-content">{{item.content}}</text>
      </view>
      <view class="msg-time">{{item.time}}</view>
    </view>

    <view class="msg-form">
      <input
        class="msg-input"
        placeholder="写下你的留言..."
        placeholder-class="placeholder-style"
        bindinput="onMessageInput"
        value="{{messageInput}}"
        confirm-type="send"
        bindconfirm="addMessage"
      />
      <view class="msg-send" bindtap="addMessage">
        <van-icon name="guide-o" class="send-icon" />
      </view>
    </view>
  </view>
</view>
