@import "../../styles/miniprogram-design.scss";

/* 现代化设计 */

/* 点菜页面 - 小程序兼容设计 */

.container {
  @include page-container;
}

.dish-page {
  @include flex;
  @include gap-3;
  height: calc(100vh - 80rpx);
  @include overflow-hidden;
}

/* 侧边导航 */
.side-nav {
  width: 160rpx;
  @include modern-card;
  @include flex;
  flex-direction: column;
  @include gap-2;
  @include shadow-md;
  height: 100%;
  flex-shrink: 0;
  @include overflow-auto;
}

.nav-item {
  @include modern-btn;
  @include btn-secondary;
  @include text-sm;
  width: 100%;

  &:active {
    @include bg-gray-100;
  }

  &.active {
    @include btn-primary;
  }
}

/* 菜品列表区域 */
.food-list-area {
  @include flex-1;
  @include modern-card;
  height: 100%;
  @include overflow-auto;
}

.food-list {
  @include flex;
  @include flex-col;
  gap: 20rpx;
  margin-top: 24rpx;
}

/* 菜品卡片 */
.food-card {
  @include modern-card;
  @include card-flat;
  @include flex;
  @include items-center;
  @include gap-3;
  @include p-3;
  @include mb-2;
  @include transition;

  &:active {
    transform: translateY(-2rpx);
    @include shadow-lg;
  }
}

.food-img {
  width: 120rpx;
  height: 120rpx;
  @include rounded-lg;
  @include shadow-sm;
  flex-shrink: 0;
  border: 2rpx solid $primary-solid;
  object-fit: cover;
}

.food-info {
  @include flex-1;
  @include flex;
  @include flex-col;
  @include justify-center;
}

.food-name {
  @include text-lg;
  @include font-bold;
  @include text-primary;
  @include mb-1;
  text-align: left;
}

.food-desc {
  @include text-gray-600;
  @include text-sm;
  @include mb-2;
  text-align: left;
  min-height: 1.8em;
}

.food-card-bottom {
  @include flex;
  @include justify-between;
  @include items-center;
  width: 100%;
}

.food-price {
  @include text-secondary;
  @include font-bold;
  @include text-lg;
}

.order-btn {
  @include modern-btn;
  @include btn-primary;
  @include btn-sm;
  min-width: 80rpx;

  &.adding {
    @include btn-success;
    transform: scale(1.05);
  }

  .success-icon {
    @include text-white;
    @include font-bold;
  }
}

.order-btn-hover {
  opacity: 0.8;
  transform: scale(0.95);
}

/* 添加食物卡片的悬停效果 */
.food-card {
  transition:
    transform 0.3s ease,
    box-shadow 0.3s ease;

  &:active {
    transform: translateY(-2rpx);
    box-shadow: 0 8px 24px 0 rgba(0, 242, 234, 0.2);
  }
}

.basket-fab {
  position: fixed;
  left: 78vw;
  bottom: 120rpx;
  z-index: 100;
  @include modern-btn;
  @include btn-gradient;
  @include rounded-full;
  width: 112rpx;
  height: 112rpx;
  @include flex;
  @include items-center;
  @include justify-center;
  @include shadow-xl;
  @include transition;

  &:active {
    transform: scale(0.95);
  }

  .van-icon {
    @include text-white;
    font-size: 48rpx;
  }
}

.basket-count {
  @include absolute;
  top: 11rpx;
  right: 11rpx;
  @include modern-badge;
  @include badge-warning;
  @include text-white;
  @include font-bold;
  min-width: 36rpx;
  height: 36rpx;
  @include text-xs;
  border: 3rpx solid $white;
  @include shadow-sm;
}

/* 点菜页面特定样式 */
.dish-page {
  @include flex;
  @include gap-3;
  height: calc(100vh - 120rpx);
  @include overflow-hidden;
}

.side-nav {
  width: 160rpx;
  @include modern-card;
  @include flex;
  @include flex-col;
  @include gap-2;
  height: 100%;
  flex-shrink: 0;
  @include overflow-auto;
}

.nav-item {
  @include modern-btn;
  @include btn-secondary;
  @include text-sm;

  &.active {
    @include btn-primary;
  }
}

.food-list-area {
  @include flex-1;
  @include modern-card;
  height: 100%;
  @include overflow-auto;
}

.food-item {
  @include modern-card;
  @include card-flat;
  @include flex;
  @include items-center;
  @include gap-3;
  @include p-3;
  @include mb-2;
}
