const app = getApp();

Page({
  data: {
    notifications: [],
    filteredNotifications: [],
    activeFilter: 'all', // all, unread, read
    totalCount: 0,
    unreadCount: 0,
    readCount: 0,
    loading: false,
    hasMore: true,
    page: 1,
    pageSize: 20
  },

  onLoad() {
    this.loadNotifications();
  },

  onShow() {
    // 每次显示页面时刷新数据
    this.loadNotifications();
  },

  // 加载通知列表
  async loadNotifications() {
    if (this.data.loading) return;

    this.setData({loading: true});

    try {
      // 模拟 API 调用
      const mockNotifications = this.generateMockNotifications();

      const notifications = mockNotifications.map(item => ({
        ...item,
        timeText: this.formatTime(item.createdAt)
      }));

      this.setData({
        notifications,
        totalCount: notifications.length,
        unreadCount: notifications.filter(item => !item.read).length,
        readCount: notifications.filter(item => item.read).length,
        loading: false
      });

      this.filterNotifications();
    } catch (error) {
      console.error('加载通知失败:', error);
      if (app.ui) {
        app.ui.showError('加载通知失败');
      } else {
        wx.showToast({
          title: '加载失败',
          icon: 'none'
        });
      }
      this.setData({loading: false});
    }
  },

  // 生成模拟通知数据
  generateMockNotifications() {
    const types = ['system', 'order', 'message'];
    const titles = {
      system: ['系统维护通知', '版本更新提醒', '安全提醒'],
      order: ['订单确认', '配送通知', '订单完成'],
      message: ['新消息提醒', '家庭留言', '回复通知']
    };
    const messages = {
      system: [
        '系统将于今晚进行维护，预计2小时',
        '发现新版本，建议及时更新',
        '请注意账户安全'
      ],
      order: [
        '您的订单已确认，正在准备中',
        '您的订单正在配送途中',
        '订单已完成，感谢您的使用'
      ],
      message: [
        '您有新的消息，请及时查看',
        '家人给您留言了',
        '您的留言收到了回复'
      ]
    };

    const notifications = [];
    for (let i = 0; i < 15; i++) {
      const type = types[Math.floor(Math.random() * types.length)];
      const titleIndex = Math.floor(Math.random() * titles[type].length);
      const messageIndex = Math.floor(Math.random() * messages[type].length);

      notifications.push({
        id: i + 1,
        type,
        title: titles[type][titleIndex],
        message: messages[type][messageIndex],
        read: Math.random() > 0.4, // 60% 已读
        createdAt: new Date(
          Date.now() - Math.random() * 7 * 24 * 60 * 60 * 1000
        ) // 最近7天
      });
    }

    return notifications.sort((a, b) => b.createdAt - a.createdAt);
  },

  // 格式化时间
  formatTime(date) {
    const now = new Date();
    const diff = now - date;
    const minutes = Math.floor(diff / (1000 * 60));
    const hours = Math.floor(diff / (1000 * 60 * 60));
    const days = Math.floor(diff / (1000 * 60 * 60 * 24));

    if (minutes < 1) return '刚刚';
    if (minutes < 60) return `${minutes}分钟前`;
    if (hours < 24) return `${hours}小时前`;
    if (days < 7) return `${days}天前`;

    return date.toLocaleDateString();
  },

  // 设置筛选条件
  setFilter(e) {
    const filter = e.currentTarget.dataset.filter;
    this.setData({activeFilter: filter});
    this.filterNotifications();
  },

  // 筛选通知
  filterNotifications() {
    const {notifications, activeFilter} = this.data;
    let filtered = notifications;

    if (activeFilter === 'unread') {
      filtered = notifications.filter(item => !item.read);
    } else if (activeFilter === 'read') {
      filtered = notifications.filter(item => item.read);
    }

    this.setData({filteredNotifications: filtered});
  },

  // 标记为已读
  async markAsRead(e) {
    const id = e.currentTarget.dataset.id;

    try {
      // 先更新本地状态
      const notifications = this.data.notifications.map(item => {
        if (item.id === id && !item.read) {
          return {...item, read: true};
        }
        return item;
      });

      this.setData({
        notifications,
        unreadCount: notifications.filter(item => !item.read).length,
        readCount: notifications.filter(item => item.read).length
      });

      this.filterNotifications();

      // 调用 API 标记为已读
      try {
        const {notificationApi} = require('../../services/api');
        await notificationApi.markAsRead(id);
        console.log(`标记通知 ${id} 为已读成功`);
      } catch (apiError) {
        console.log('API 调用失败，使用模拟数据:', apiError.message);
      }
    } catch (error) {
      console.error('标记已读失败:', error);
      // 如果 API 调用失败，可以选择回滚本地状态
    }
  },

  // 全部标记为已读
  async markAllRead() {
    if (this.data.unreadCount === 0) {
      wx.showToast({
        title: '没有未读通知',
        icon: 'none'
      });
      return;
    }

    try {
      wx.showLoading({title: '处理中...'});

      const notifications = this.data.notifications.map(item => ({
        ...item,
        read: true
      }));

      this.setData({
        notifications,
        unreadCount: 0,
        readCount: notifications.length
      });

      this.filterNotifications();

      wx.showToast({
        title: '已全部标记为已读',
        icon: 'success'
      });

      // 调用 API 全部标记为已读
      try {
        const {notificationApi} = require('../../services/api');
        await notificationApi.markAllRead();
        console.log('全部标记为已读成功');
      } catch (apiError) {
        console.log('API 调用失败，使用模拟数据:', apiError.message);
      }
    } catch (error) {
      console.error('标记失败:', error);
      wx.showToast({
        title: '操作失败',
        icon: 'error'
      });
    } finally {
      wx.hideLoading();
    }
  },

  // 加载更多
  loadMore() {
    // 这里可以实现分页加载
    console.log('加载更多通知');
  },

  // 返回上一页
  goBack() {
    wx.navigateBack();
  }
});
