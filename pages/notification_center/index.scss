/* 通知中心页面 - 现代化设计 */
@import "../../styles/miniprogram-design.scss";

.container {
  @include page-container;
  @include page-container-safe;
  
  /* 隐藏滚动条 */
  -ms-overflow-style: none;
  scrollbar-width: none;
  &::-webkit-scrollbar {
    display: none;
  }
}

/* 头部导航 */
.page-header {
  @include modern-card;
  @include flex;
  @include justify-between;
  @include items-center;
  @include mb-3;
  padding: 16rpx 24rpx;
  height: 88rpx;
  
  .header-left, .header-right {
    width: 120rpx;
    @include flex;
    @include items-center;
  }
  
  .header-left {
    @include justify-start;
    
    .van-icon {
      @include text-gray-600;
      @include transition;
      
      &:active {
        @include text-primary;
        transform: scale(0.9);
      }
    }
  }
  
  .header-title {
    @include flex;
    @include items-center;
    @include gap-2;
    @include text-lg;
    @include font-semibold;
    @include text-primary;
    
    .van-icon {
      @include text-secondary;
    }
  }
  
  .header-right {
    @include justify-end;
  }
  
  .mark-all-read {
    @include modern-btn;
    @include btn-secondary;
    @include text-xs;
    padding: 8rpx 16rpx;
    height: 48rpx;
    @include rounded-full;
    
    &:active {
      @include btn-primary;
    }
  }
}

/* 统计卡片 */
.stats-card {
  @include modern-card;
  @include card-primary;
  @include flex;
  @include justify-between;
  @include items-center;
  @include mb-4;
  padding: 32rpx;
  
  .stat-item {
    @include flex;
    @include flex-col;
    @include items-center;
    @include gap-2;
    @include flex-1;
  }
  
  .stat-number {
    @include text-2xl;
    @include font-bold;
    @include text-primary;
    
    &.unread {
      @include text-warning;
    }
  }
  
  .stat-label {
    @include text-xs;
    @include text-gray-600;
  }
  
  .stat-divider {
    width: 2rpx;
    height: 60rpx;
    background: $gray-200;
  }
}

/* 筛选标签 */
.filter-tabs {
  @include flex;
  @include gap-2;
  @include mb-4;
  padding: 0 8rpx;
  
  .filter-tab {
    @include modern-btn;
    @include btn-secondary;
    @include text-sm;
    @include font-medium;
    @include flex-1;
    @include text-center;
    padding: 16rpx 24rpx;
    @include rounded-full;
    @include transition;
    
    &.active {
      @include btn-primary;
    }
    
    &:active {
      transform: scale(0.98);
    }
  }
}

/* 通知列表 */
.notification-list {
  @include flex;
  @include flex-col;
  @include gap-2;
}

.notification-item {
  @include modern-card;
  @include card-flat;
  @include flex;
  @include items-start;
  @include gap-3;
  padding: 24rpx;
  @include transition;
  
  &.unread {
    @include card-primary;
    border-left: 6rpx solid $primary-solid;
  }
  
  &.read {
    opacity: 0.7;
  }
  
  &:active {
    transform: translateY(-2rpx);
    @include shadow-md;
  }
  
  .notification-icon {
    @include flex;
    @include items-center;
    @include justify-center;
    width: 64rpx;
    height: 64rpx;
    @include rounded-full;
    background: rgba($primary-solid, 0.1);
    flex-shrink: 0;
  }
  
  .notification-content {
    @include flex-1;
    @include flex;
    @include flex-col;
    @include gap-2;
    min-width: 0;
  }
  
  .notification-title {
    @include text-base;
    @include font-semibold;
    @include text-gray-900;
    @include overflow-hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
  }
  
  .notification-message {
    @include text-sm;
    @include text-gray-600;
    line-height: 1.5;
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    @include overflow-hidden;
  }
  
  .notification-time {
    @include text-xs;
    @include text-gray-400;
  }
  
  .notification-status {
    @include flex;
    @include items-center;
    @include justify-center;
    width: 32rpx;
    height: 32rpx;
    flex-shrink: 0;
  }
  
  .unread-dot {
    width: 16rpx;
    height: 16rpx;
    @include rounded-full;
    background: $warning;
    @include shadow-sm;
  }
}

/* 空状态 */
.empty-state {
  @include flex;
  @include flex-col;
  @include items-center;
  @include justify-center;
  padding: 80rpx 32rpx;
  @include text-center;
  
  .empty-title {
    @include text-lg;
    @include font-semibold;
    @include text-gray-700;
    @include mb-2;
    margin-top: 24rpx;
  }
  
  .empty-description {
    @include text-sm;
    @include text-gray-500;
    line-height: 1.6;
  }
}

/* 加载更多 */
.load-more {
  @include flex;
  @include items-center;
  @include justify-center;
  padding: 32rpx;
  @include text-sm;
  @include text-gray-600;
  @include transition;
  
  &:active {
    @include text-primary;
  }
}
