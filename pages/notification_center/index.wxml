<view class="container">
  <!-- 统计卡片 -->
  <view class="stats-card">
    <view class="stat-item">
      <view class="stat-number">{{totalCount}}</view>
      <view class="stat-label">总通知</view>
    </view>
    <view class="stat-divider"></view>
    <view class="stat-item">
      <view class="stat-number unread">{{unreadCount}}</view>
      <view class="stat-label">未读</view>
    </view>
    <view class="stat-divider"></view>
    <view class="stat-item">
      <view class="stat-number">{{readCount}}</view>
      <view class="stat-label">已读</view>
    </view>
  </view>

  <!-- 筛选标签 -->
  <view class="filter-tabs">
    <view class="filter-tab {{activeFilter === 'all' ? 'active' : ''}}" bindtap="setFilter" data-filter="all">
      全部
    </view>
    <view class="filter-tab {{activeFilter === 'unread' ? 'active' : ''}}" bindtap="setFilter" data-filter="unread">
      未读
    </view>
    <view class="filter-tab {{activeFilter === 'read' ? 'active' : ''}}" bindtap="setFilter" data-filter="read">
      已读
    </view>
  </view>

  <!-- 通知列表 -->
  <view class="notification-list">
    <view class="notification-item {{item.read ? 'read' : 'unread'}}" wx:for="{{filteredNotifications}}" wx:key="id" bindtap="markAsRead" data-id="{{item.id}}">
      <view class="notification-icon">
        <van-icon name="{{item.type === 'system' ? 'setting-o' : item.type === 'order' ? 'shop-o' : 'chat-o'}}" size="24px" color="{{item.read ? '#94A3B8' : '#6366F1'}}" />
      </view>
      <view class="notification-content">
        <view class="notification-title">{{item.title}}</view>
        <view class="notification-message">{{item.message}}</view>
        <view class="notification-time">{{item.timeText}}</view>
      </view>
      <view class="notification-status" wx:if="{{!item.read}}">
        <view class="unread-dot"></view>
      </view>
    </view>

    <!-- 空状态 -->
    <view class="empty-state" wx:if="{{filteredNotifications.length === 0}}">
      <van-icon name="bell-o" size="80px" color="#CBD5E1" />
      <view class="empty-title">暂无通知</view>
      <view class="empty-description">
        {{activeFilter === 'all' ? '还没有收到任何通知' :
        activeFilter === 'unread' ? '没有未读通知' : '没有已读通知'}}
      </view>
    </view>
  </view>

  <!-- 加载更多 -->
  <view class="load-more" wx:if="{{hasMore && filteredNotifications.length > 0}}">
    <van-loading size="20px" color="#6366F1" wx:if="{{loading}}" />
    <text wx:else bindtap="loadMore">加载更多</text>
  </view>
</view>