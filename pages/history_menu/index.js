const {menuApi, orderApi} = require('../../services/api');

Page({
  data: {
    historyMenus: [],
    loading: true,
    viewingToday: false,
    loadMoreStatus: 'none', // none, loading, nomore, error
    currentPage: 1,
    pageSize: 10,
    hasMore: true
  },

  async onLoad(options) {
    // 检查是否是查看今日菜单
    const viewToday = options && options.viewToday === 'true';

    if (viewToday) {
      this.setData({viewingToday: true});
      await this.loadTodayOrders();
    } else {
      await this.loadHistoryMenus();
    }
  },

  // 加载今日订单
  async loadTodayOrders() {
    try {
      wx.showLoading({title: '加载中...'});

      const result = await orderApi.getTodayOrders();

      if (result.code === 200 && result.data.length > 0) {
        const formattedMenus = result.data.map(order => ({
          id: order.id,
          date: this.formatOrderDate(order.diningTime),
          summary: this.formatOrderSummary(order.items),
          remark: order.remark || '无',
          status: order.status,
          isToday: true
        }));

        this.setData({
          historyMenus: formattedMenus,
          loading: false
        });
      } else {
        // 如果没有今日订单，检查本地缓存
        const todayMenu = wx.getStorageSync('todayMenu');
        if (todayMenu) {
          const formattedMenu = {
            id: todayMenu.id || Date.now(),
            date: todayMenu.date || '今日',
            summary: this.formatSummary(todayMenu.dishes),
            remark: todayMenu.remark || '无',
            isToday: true
          };

          this.setData({
            historyMenus: [formattedMenu],
            loading: false
          });
        } else {
          this.setData({
            historyMenus: [],
            loading: false
          });
        }
      }
    } catch (error) {
      console.error('加载今日订单失败:', error);
      this.setData({loading: false});
      wx.showToast({
        title: '加载失败',
        icon: 'error'
      });
    } finally {
      wx.hideLoading();
    }
  },

  // 加载历史菜单
  async loadHistoryMenus() {
    try {
      wx.showLoading({title: '加载中...'});

      const result = await menuApi.getHistoryMenus();

      if (result.code === 200 && result.data.length > 0) {
        const formattedMenus = result.data.map(menu => ({
          id: menu.id,
          date: this.formatMenuDate(menu.date),
          summary: this.formatMenuSummary(menu.dishes),
          remark: menu.remark || '无'
        }));

        this.setData({
          historyMenus: formattedMenus,
          loading: false
        });
      } else {
        // 如果API没有数据，检查本地缓存
        const historyMenus = wx.getStorageSync('historyMenus') || [];

        if (historyMenus.length > 0) {
          const formattedMenus = historyMenus.map(menu => ({
            id: menu.id || Math.random().toString(36).substr(2, 9),
            date: menu.date,
            summary: this.formatSummary(menu.dishes),
            remark: menu.remark || '无'
          }));

          this.setData({
            historyMenus: formattedMenus,
            loading: false
          });
        } else {
          this.setData({
            historyMenus: [],
            loading: false
          });
        }
      }
    } catch (error) {
      console.error('加载历史菜单失败:', error);
      this.setData({loading: false});
      wx.showToast({
        title: '加载失败',
        icon: 'error'
      });
    } finally {
      wx.hideLoading();
    }
  },

  // 格式化菜品摘要
  formatSummary(dishes) {
    if (!dishes || !dishes.length) {
      return '暂无菜品';
    }

    return dishes.map(dish => `${dish.name}x${dish.count}`).join('、');
  },

  // 格式化订单摘要
  formatOrderSummary(items) {
    if (!items) {
      return '暂无菜品';
    }

    try {
      const itemsArray = typeof items === 'string' ? JSON.parse(items) : items;
      return itemsArray
        .map(item => `${item.dishName}x${item.count}`)
        .join('、');
    } catch (error) {
      return '菜品信息解析失败';
    }
  },

  // 格式化菜单摘要
  formatMenuSummary(dishes) {
    if (!dishes || !dishes.length) {
      return '暂无菜品';
    }

    return dishes
      .map(dish => `${dish.dish?.name || dish.name}x${dish.count}`)
      .join('、');
  },

  // 格式化订单日期
  formatOrderDate(dateString) {
    const date = new Date(dateString);
    const now = new Date();
    const today = new Date(now.getFullYear(), now.getMonth(), now.getDate());
    const orderDate = new Date(
      date.getFullYear(),
      date.getMonth(),
      date.getDate()
    );

    const diffDays = Math.floor((orderDate - today) / (24 * 60 * 60 * 1000));

    let dayText = '';
    if (diffDays === 0) {
      dayText = '今天';
    } else if (diffDays === -1) {
      dayText = '昨天';
    } else if (diffDays === 1) {
      dayText = '明天';
    } else {
      dayText = `${date.getMonth() + 1}-${date.getDate()}`;
    }

    const hour = date.getHours();
    let mealTime = '';
    if (hour < 10) {
      mealTime = '早餐';
    } else if (hour < 16) {
      mealTime = '午餐';
    } else {
      mealTime = '晚餐';
    }

    return `${dayText} ${mealTime}`;
  },

  // 格式化菜单日期
  formatMenuDate(dateString) {
    const date = new Date(dateString);
    return `${date.getMonth() + 1}-${date.getDate()}`;
  },

  // 下拉刷新
  async onRefresh() {
    try {
      // 重置分页
      this.setData({
        currentPage: 1,
        hasMore: true,
        loadMoreStatus: 'none'
      });

      // 重新加载数据
      if (this.data.viewingToday) {
        await this.loadTodayOrders();
      } else {
        await this.loadHistoryMenus();
      }

      // 停止刷新动画
      this.selectComponent('refresh-list').stopRefresh();
    } catch (error) {
      console.error('刷新失败:', error);
      this.selectComponent('refresh-list').stopRefresh();
      wx.showToast({
        title: '刷新失败',
        icon: 'error'
      });
    }
  },

  // 上拉加载更多
  async onLoadMore() {
    if (!this.data.hasMore || this.data.loadMoreStatus === 'loading') {
      return;
    }

    this.setData({loadMoreStatus: 'loading'});

    try {
      const nextPage = this.data.currentPage + 1;
      let result;

      if (this.data.viewingToday) {
        // 今日菜单通常只有一页，不需要分页
        this.setData({
          loadMoreStatus: 'nomore',
          hasMore: false
        });
        return;
      } else {
        result = await menuApi.getHistoryMenus({
          page: nextPage,
          pageSize: this.data.pageSize
        });
      }

      if (result.code === 200) {
        const newMenus = result.data || [];

        if (newMenus.length === 0) {
          this.setData({
            loadMoreStatus: 'nomore',
            hasMore: false
          });
        } else {
          const formattedMenus = newMenus.map(menu => ({
            id: menu.id,
            date: this.formatMenuDate(menu.date),
            summary: this.formatMenuSummary(menu.dishes),
            remark: menu.remark || '无',
            dishes: menu.dishes || []
          }));

          this.setData({
            historyMenus: [...this.data.historyMenus, ...formattedMenus],
            currentPage: nextPage,
            loadMoreStatus:
              newMenus.length < this.data.pageSize ? 'nomore' : 'none',
            hasMore: newMenus.length >= this.data.pageSize
          });
        }
      } else {
        throw new Error(result.message || '加载失败');
      }
    } catch (error) {
      console.error('加载更多失败:', error);
      this.setData({loadMoreStatus: 'error'});
      wx.showToast({
        title: '加载失败，点击重试',
        icon: 'none'
      });
    }
  },

  // 空状态操作
  onEmptyAction() {
    this.onRefresh();
  },

  // 返回上一页
  goBack() {
    wx.navigateBack();
  }
});
