<view class="container">
  <view class="mine-user-card">
    <view class="mine-user-avatar">
      <image
        wx:if="{{userInfo.avatar}}"
        src="{{userInfo.avatar}}"
        mode="aspectFill"
      />
      <van-icon wx:else name="user-o" size="48rpx" color="#6366F1" />
    </view>
    <view class="mine-user-info">
      <view class="mine-user-name">{{userInfo.name || '用户'}}</view>
      <view class="mine-user-phone" wx:if="{{formattedPhone}}"
        >📱 {{formattedPhone}}</view
      >
      <view class="mine-user-role" wx:if="{{userInfo.role}}"
        >👤 {{roleText}}</view
      >
      <view class="mine-user-id">🆔 ID: {{userInfo.id || '未知'}}</view>
    </view>
    <view class="mine-user-badge">
      <van-icon name="vip-card-o" size="24rpx" color="#F59E0B" />
    </view>
  </view>

  <view class="mine-links-section">
    <button class="mine-link-btn message" bindtap="goToFamilyMessage">
      <van-icon name="chat-o" size="36rpx" />
      <text style="margin-left: 16rpx;">家庭留言</text>
    </button>

    <button class="mine-link-btn notice" bindtap="goToNotificationCenter">
      <van-icon name="bell-o" size="36rpx" />
      <text style="margin-left: 16rpx;">通知中心</text>
    </button>
  </view>

  <view class="mine-action-card">
    <button class="mine-action-btn logout-btn" bindtap="onLogout">
      <van-icon name="sign-out" size="32rpx" />
      <text>退出登录</text>
    </button>
  </view>
</view>
