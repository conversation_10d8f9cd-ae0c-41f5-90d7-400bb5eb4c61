// 测试辅助工具函数

/**
 * 创建模拟的页面实例
 * @param {Object} pageOptions 页面配置
 * @param {Object} initialData 初始数据
 * @returns {Object} 页面实例
 */
function createMockPage(pageOptions, initialData = {}) {
  const page = {
    data: { ...pageOptions.data, ...initialData },
    setData: jest.fn(function(newData, callback) {
      Object.assign(this.data, newData);
      if (callback) callback();
    }),
    ...pageOptions
  };
  
  // 绑定方法的 this 上下文
  Object.keys(pageOptions).forEach(key => {
    if (typeof pageOptions[key] === 'function') {
      page[key] = pageOptions[key].bind(page);
    }
  });
  
  return page;
}

/**
 * 模拟网络请求
 * @param {Object} mockResponse 模拟响应数据
 */
function mockRequest(mockResponse) {
  wx.request.mockImplementation((options) => {
    setTimeout(() => {
      if (options.success) {
        options.success({
          statusCode: 200,
          data: mockResponse
        });
      }
    }, 10);
  });
}

/**
 * 模拟网络请求失败
 * @param {string} errorMessage 错误信息
 */
function mockRequestError(errorMessage = '网络错误') {
  wx.request.mockImplementation((options) => {
    setTimeout(() => {
      if (options.fail) {
        options.fail({
          errMsg: errorMessage
        });
      }
    }, 10);
  });
}

/**
 * 等待异步操作完成
 * @param {number} ms 等待时间（毫秒）
 */
function waitFor(ms = 100) {
  return new Promise(resolve => setTimeout(resolve, ms));
}

/**
 * 模拟用户点击事件
 * @param {Object} dataset 数据集
 * @param {Object} target 目标元素
 */
function createMockEvent(dataset = {}, target = {}) {
  return {
    currentTarget: {
      dataset,
      ...target
    },
    target: {
      dataset,
      ...target
    },
    detail: {}
  };
}

/**
 * 模拟输入事件
 * @param {string} value 输入值
 */
function createMockInputEvent(value) {
  return {
    detail: {
      value
    }
  };
}

/**
 * 模拟存储数据
 * @param {Object} data 存储的数据
 */
function mockStorage(data) {
  Object.keys(data).forEach(key => {
    wx.getStorageSync.mockImplementation((storageKey) => {
      return storageKey === key ? data[key] : '';
    });
  });
}

/**
 * 清除所有模拟
 */
function clearAllMocks() {
  jest.clearAllMocks();
  
  // 重置 wx API 的默认行为
  wx.getStorageSync.mockReturnValue('');
  wx.request.mockImplementation((options) => {
    setTimeout(() => {
      if (options.success) {
        options.success({
          statusCode: 200,
          data: { code: 200, data: {}, message: 'success' }
        });
      }
    }, 100);
  });
}

/**
 * 验证页面数据
 * @param {Object} page 页面实例
 * @param {Object} expectedData 期望的数据
 */
function expectPageData(page, expectedData) {
  Object.keys(expectedData).forEach(key => {
    expect(page.data[key]).toEqual(expectedData[key]);
  });
}

/**
 * 验证 wx API 调用
 * @param {Function} apiMethod wx API 方法
 * @param {Array} expectedCalls 期望的调用参数
 */
function expectWxApiCalls(apiMethod, expectedCalls) {
  expect(apiMethod).toHaveBeenCalledTimes(expectedCalls.length);
  expectedCalls.forEach((call, index) => {
    expect(apiMethod).toHaveBeenNthCalledWith(index + 1, call);
  });
}

module.exports = {
  createMockPage,
  mockRequest,
  mockRequestError,
  waitFor,
  createMockEvent,
  createMockInputEvent,
  mockStorage,
  clearAllMocks,
  expectPageData,
  expectWxApiCalls
};
