// 点菜页面测试
const {
  createMockPage,
  mockRequest,
  waitFor,
  createMockEvent,
  clearAllMocks,
  expectPageData
} = require('../utils/testHelpers');

// 模拟 API 服务
jest.mock('../../services/api', () => ({
  dishApi: {
    getDishesByCategory: jest.fn(),
    getDishDetail: jest.fn()
  }
}));

const { dishApi } = require('../../services/api');

describe('点菜页面测试', () => {
  let orderPage;
  
  // 模拟点菜页面配置
  const orderPageOptions = {
    data: {
      currentType: 'hot',
      basketCount: 0,
      categories: [
        {type: 'hot', name: '热菜'},
        {type: 'cold', name: '凉菜'},
        {type: 'soup', name: '汤品'},
        {type: 'staple', name: '主食'},
        {type: 'dessert', name: '甜品'}
      ],
      loading: true,
      foodData: {},
      foodList: []
    },
    
    async onLoad() {
      await this.loadDishes();
      this.renderFoodList('hot');
      this.updateBasketCount();
    },
    
    async loadDishes() {
      try {
        wx.showLoading({title: '加载中...'});
        
        const result = await dishApi.getDishesByCategory();
        
        if (result.code === 200) {
          this.setData({
            foodData: result.data,
            loading: false
          });
        } else {
          wx.showToast({
            title: '加载失败',
            icon: 'error'
          });
        }
      } catch (error) {
        console.error('加载菜品失败:', error);
        wx.showToast({
          title: '网络错误',
          icon: 'error'
        });
      } finally {
        wx.hideLoading();
      }
    },
    
    switchCategory(e) {
      const type = e.currentTarget.dataset.type;
      this.setData({
        currentType: type
      });
      this.renderFoodList(type);
    },
    
    renderFoodList(type) {
      const foodList = this.data.foodData[type] || [];
      
      const enhancedFoodList = foodList.map(item => ({
        ...item,
        isAdding: false
      }));
      
      this.setData({
        foodList: enhancedFoodList
      });
    },
    
    async goToDetail(e) {
      const id = e.currentTarget.dataset.id;
      
      try {
        wx.showLoading({title: '加载中...'});
        
        const result = await dishApi.getDishDetail(id);
        
        if (result.code === 200) {
          wx.setStorageSync('detailData', result.data);
          
          wx.navigateTo({
            url: '/pages/detail/index'
          });
        } else {
          wx.showToast({
            title: '获取详情失败',
            icon: 'error'
          });
        }
      } catch (error) {
        console.error('获取菜品详情失败:', error);
        wx.showToast({
          title: '网络错误',
          icon: 'error'
        });
      } finally {
        wx.hideLoading();
      }
    },
    
    addToBasket(e) {
      const { id, name, remark, img, index } = e.currentTarget.dataset;
      
      // 获取当前购物篮
      let basket = wx.getStorageSync('basket') || {};
      
      // 添加或更新菜品
      if (basket[id]) {
        basket[id].count += 1;
      } else {
        basket[id] = {
          id,
          name,
          remark,
          img,
          count: 1
        };
      }
      
      // 保存到缓存
      wx.setStorageSync('basket', basket);
      
      // 显示添加动画
      const foodList = this.data.foodList;
      foodList[index].isAdding = true;
      this.setData({ foodList });
      
      // 更新购物篮数量
      this.updateBasketCount();
      
      // 重置动画状态
      setTimeout(() => {
        foodList[index].isAdding = false;
        this.setData({ foodList });
      }, 1000);
    },
    
    updateBasketCount() {
      const basket = wx.getStorageSync('basket') || {};
      const total = Object.values(basket).reduce(
        (sum, item) => sum + item.count,
        0
      );
      
      this.setData({
        basketCount: total
      });
    },
    
    goToBasket() {
      wx.navigateTo({
        url: '/pages/today_order/index'
      });
    }
  };
  
  beforeEach(() => {
    clearAllMocks();
    orderPage = createMockPage(orderPageOptions);
  });
  
  describe('页面初始化', () => {
    test('应该正确初始化页面数据', () => {
      expect(orderPage.data.currentType).toBe('hot');
      expect(orderPage.data.basketCount).toBe(0);
      expect(orderPage.data.loading).toBe(true);
      expect(orderPage.data.categories).toHaveLength(5);
    });
    
    test('应该加载菜品数据', async () => {
      const mockData = {
        hot: [
          { id: 1, name: '宫保鸡丁', img: 'test.jpg' },
          { id: 2, name: '麻婆豆腐', img: 'test2.jpg' }
        ],
        cold: [
          { id: 3, name: '凉拌黄瓜', img: 'test3.jpg' }
        ]
      };
      
      dishApi.getDishesByCategory.mockResolvedValue({
        code: 200,
        data: mockData
      });
      
      await orderPage.onLoad();
      await waitFor(50);
      
      expect(dishApi.getDishesByCategory).toHaveBeenCalled();
      expect(orderPage.data.foodData).toEqual(mockData);
      expect(orderPage.data.loading).toBe(false);
      expect(orderPage.data.foodList).toHaveLength(2);
    });
    
    test('加载菜品失败应该显示错误', async () => {
      dishApi.getDishesByCategory.mockRejectedValue(new Error('网络错误'));
      
      await orderPage.onLoad();
      await waitFor(50);
      
      expect(wx.showToast).toHaveBeenCalledWith({
        title: '网络错误',
        icon: 'error'
      });
    });
  });
  
  describe('分类切换', () => {
    beforeEach(async () => {
      const mockData = {
        hot: [
          { id: 1, name: '宫保鸡丁', img: 'test.jpg' },
          { id: 2, name: '麻婆豆腐', img: 'test2.jpg' }
        ],
        cold: [
          { id: 3, name: '凉拌黄瓜', img: 'test3.jpg' }
        ]
      };
      
      dishApi.getDishesByCategory.mockResolvedValue({
        code: 200,
        data: mockData
      });
      
      await orderPage.onLoad();
      await waitFor(50);
    });
    
    test('应该能够切换到凉菜分类', () => {
      const event = createMockEvent({ type: 'cold' });
      orderPage.switchCategory(event);
      
      expect(orderPage.data.currentType).toBe('cold');
      expect(orderPage.data.foodList).toHaveLength(1);
      expect(orderPage.data.foodList[0].name).toBe('凉拌黄瓜');
    });
    
    test('应该能够切换到汤品分类', () => {
      const event = createMockEvent({ type: 'soup' });
      orderPage.switchCategory(event);
      
      expect(orderPage.data.currentType).toBe('soup');
      expect(orderPage.data.foodList).toHaveLength(0);
    });
  });
  
  describe('菜品详情', () => {
    test('应该能够跳转到菜品详情页', async () => {
      const mockDetail = {
        id: 1,
        name: '宫保鸡丁',
        description: '经典川菜',
        img: 'test.jpg'
      };
      
      dishApi.getDishDetail.mockResolvedValue({
        code: 200,
        data: mockDetail
      });
      
      const event = createMockEvent({ id: '1' });
      await orderPage.goToDetail(event);
      await waitFor(50);
      
      expect(dishApi.getDishDetail).toHaveBeenCalledWith('1');
      expect(wx.setStorageSync).toHaveBeenCalledWith('detailData', mockDetail);
      expect(wx.navigateTo).toHaveBeenCalledWith({
        url: '/pages/detail/index'
      });
    });
    
    test('获取详情失败应该显示错误', async () => {
      dishApi.getDishDetail.mockRejectedValue(new Error('网络错误'));
      
      const event = createMockEvent({ id: '1' });
      await orderPage.goToDetail(event);
      await waitFor(50);
      
      expect(wx.showToast).toHaveBeenCalledWith({
        title: '网络错误',
        icon: 'error'
      });
    });
  });
  
  describe('购物篮功能', () => {
    beforeEach(() => {
      // 清空购物篮
      wx.getStorageSync.mockReturnValue({});
    });
    
    test('应该能够添加菜品到购物篮', () => {
      const event = createMockEvent({
        id: '1',
        name: '宫保鸡丁',
        remark: '经典川菜',
        img: 'test.jpg',
        index: '0'
      });
      
      // 模拟空购物篮
      wx.getStorageSync.mockReturnValue({});
      
      orderPage.addToBasket(event);
      
      expect(wx.setStorageSync).toHaveBeenCalledWith('basket', {
        '1': {
          id: '1',
          name: '宫保鸡丁',
          remark: '经典川菜',
          img: 'test.jpg',
          count: 1
        }
      });
      expect(orderPage.data.basketCount).toBe(1);
    });
    
    test('应该能够增加已存在菜品的数量', () => {
      const event = createMockEvent({
        id: '1',
        name: '宫保鸡丁',
        remark: '经典川菜',
        img: 'test.jpg',
        index: '0'
      });
      
      // 模拟已有菜品的购物篮
      wx.getStorageSync.mockReturnValue({
        '1': {
          id: '1',
          name: '宫保鸡丁',
          remark: '经典川菜',
          img: 'test.jpg',
          count: 1
        }
      });
      
      orderPage.addToBasket(event);
      
      expect(wx.setStorageSync).toHaveBeenCalledWith('basket', {
        '1': {
          id: '1',
          name: '宫保鸡丁',
          remark: '经典川菜',
          img: 'test.jpg',
          count: 2
        }
      });
    });
    
    test('应该能够跳转到购物篮页面', () => {
      orderPage.goToBasket();
      
      expect(wx.navigateTo).toHaveBeenCalledWith({
        url: '/pages/today_order/index'
      });
    });
  });
});
