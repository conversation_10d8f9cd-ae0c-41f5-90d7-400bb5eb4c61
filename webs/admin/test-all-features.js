#!/usr/bin/env node

/**
 * 后台管理系统完整功能测试
 * 测试用户管理、菜品管理、订单管理、消息管理等所有CRUD功能
 */

const axios = require('axios');

// 配置
const BASE_URL = 'http://localhost:3000/api';
let ADMIN_TOKEN = ''; // 需要通过登录获取

// 创建axios实例
const api = axios.create({
  baseURL: BASE_URL,
  headers: {
    'Content-Type': 'application/json'
  }
});

// 添加请求拦截器
api.interceptors.request.use(config => {
  if (ADMIN_TOKEN) {
    config.headers.Authorization = `Bearer ${ADMIN_TOKEN}`;
  }
  return config;
});

// 测试结果统计
let testResults = {
  total: 0,
  passed: 0,
  failed: 0,
  errors: []
};

// 测试工具函数
function logTest(name, success, error = null) {
  testResults.total++;
  if (success) {
    testResults.passed++;
    console.log(`✅ ${name}`);
  } else {
    testResults.failed++;
    console.log(`❌ ${name}: ${error}`);
    testResults.errors.push({ name, error });
  }
}

// 延迟函数
const delay = (ms) => new Promise(resolve => setTimeout(resolve, ms));

// 1. 登录获取token
async function login() {
  try {
    const response = await api.post('/auth/login', {
      phone: '13800000000',
      password: '123456'
    });
    
    if (response.data.success && response.data.data.token) {
      ADMIN_TOKEN = response.data.data.token;
      logTest('管理员登录', true);
      return true;
    } else {
      logTest('管理员登录', false, '登录失败');
      return false;
    }
  } catch (error) {
    logTest('管理员登录', false, error.message);
    return false;
  }
}

// 2. 测试用户管理功能
async function testUserManagement() {
  console.log('\n📋 测试用户管理功能...');
  
  try {
    // 获取用户列表
    const usersResponse = await api.get('/users');
    logTest('获取用户列表', usersResponse.status === 200 && usersResponse.data.success);

    // 获取用户统计
    const statsResponse = await api.get('/users/statistics');
    logTest('获取用户统计', statsResponse.status === 200 && statsResponse.data.success);

    // 创建测试用户
    const createResponse = await api.post('/users', {
      name: '测试用户',
      phone: '13900000001',
      password: '123456',
      role: 'user'
    });
    logTest('创建用户', createResponse.status === 200 && createResponse.data.success);

    if (createResponse.data.success && createResponse.data.data) {
      const userId = createResponse.data.data.id;
      
      // 更新用户
      const updateResponse = await api.put(`/users/${userId}`, {
        name: '更新后的用户名'
      });
      logTest('更新用户', updateResponse.status === 200 && updateResponse.data.success);

      // 重置密码
      const resetResponse = await api.put(`/users/${userId}/password`, {
        password: 'newpassword123'
      });
      logTest('重置用户密码', resetResponse.status === 200 && resetResponse.data.success);

      // 删除用户
      const deleteResponse = await api.delete(`/users/${userId}`);
      logTest('删除用户', deleteResponse.status === 200 && deleteResponse.data.success);
    }

  } catch (error) {
    logTest('用户管理功能', false, error.message);
  }
}

// 3. 测试菜品管理功能
async function testDishManagement() {
  console.log('\n🍽️ 测试菜品管理功能...');
  
  try {
    // 获取菜品列表
    const dishesResponse = await api.get('/dishes');
    logTest('获取菜品列表', dishesResponse.status === 200 && dishesResponse.data.success);

    // 获取菜品分类
    const categoriesResponse = await api.get('/dishes/categories');
    logTest('获取菜品分类', categoriesResponse.status === 200 && categoriesResponse.data.success);

    // 获取菜品统计
    const statsResponse = await api.get('/dishes/statistics');
    logTest('获取菜品统计', statsResponse.status === 200 && statsResponse.data.success);

    // 创建测试菜品
    const createResponse = await api.post('/dishes', {
      name: '测试菜品',
      description: '这是一个测试菜品',
      category: '热菜',
      image: 'https://example.com/test.jpg'
    });
    logTest('创建菜品', createResponse.status === 200 && createResponse.data.success);

    if (createResponse.data.success && createResponse.data.data) {
      const dishId = createResponse.data.data.id;
      
      // 获取菜品详情
      const detailResponse = await api.get(`/dishes/${dishId}`);
      logTest('获取菜品详情', detailResponse.status === 200 && detailResponse.data.success);

      // 更新菜品
      const updateResponse = await api.put(`/dishes/${dishId}`, {
        name: '更新后的菜品名',
        description: '更新后的描述'
      });
      logTest('更新菜品', updateResponse.status === 200 && updateResponse.data.success);

      // 删除菜品
      const deleteResponse = await api.delete(`/dishes/${dishId}`);
      logTest('删除菜品', deleteResponse.status === 200 && deleteResponse.data.success);
    }

  } catch (error) {
    logTest('菜品管理功能', false, error.message);
  }
}

// 4. 测试订单管理功能
async function testOrderManagement() {
  console.log('\n📦 测试订单管理功能...');
  
  try {
    // 获取订单列表
    const ordersResponse = await api.get('/orders');
    logTest('获取订单列表', ordersResponse.status === 200 && ordersResponse.data.success);

    // 获取订单统计
    const statsResponse = await api.get('/orders/statistics');
    logTest('获取订单统计', statsResponse.status === 200 && statsResponse.data.success);

  } catch (error) {
    logTest('订单管理功能', false, error.message);
  }
}

// 5. 测试消息管理功能
async function testMessageManagement() {
  console.log('\n💬 测试消息管理功能...');
  
  try {
    // 获取消息列表
    const messagesResponse = await api.get('/messages');
    logTest('获取消息列表', messagesResponse.status === 200 && messagesResponse.data.success);

    // 获取消息统计
    const statsResponse = await api.get('/messages/statistics');
    logTest('获取消息统计', statsResponse.status === 200 && statsResponse.data.success);

  } catch (error) {
    logTest('消息管理功能', false, error.message);
  }
}

// 6. 测试菜单管理功能
async function testMenuManagement() {
  console.log('\n📅 测试菜单管理功能...');
  
  try {
    // 获取菜单列表
    const menusResponse = await api.get('/menus');
    logTest('获取菜单列表', menusResponse.status === 200 && menusResponse.data.success);

    // 获取今日菜单
    const todayResponse = await api.get('/menus/today');
    logTest('获取今日菜单', todayResponse.status === 200 && todayResponse.data.success);

    // 获取菜单统计
    const statsResponse = await api.get('/menus/statistics');
    logTest('获取菜单统计', statsResponse.status === 200 && statsResponse.data.success);

  } catch (error) {
    logTest('菜单管理功能', false, error.message);
  }
}

// 主测试函数
async function runAllTests() {
  console.log('🚀 开始后台管理系统完整功能测试...\n');

  try {
    // 1. 登录
    const loginSuccess = await login();
    if (!loginSuccess) {
      console.log('❌ 登录失败，无法继续测试');
      return;
    }
    await delay(500);

    // 2. 测试各个模块
    await testUserManagement();
    await delay(1000);

    await testDishManagement();
    await delay(1000);

    await testOrderManagement();
    await delay(1000);

    await testMessageManagement();
    await delay(1000);

    await testMenuManagement();
    await delay(500);

  } catch (error) {
    console.error('测试过程中发生错误:', error);
  }

  // 输出测试结果
  console.log('\n' + '='.repeat(60));
  console.log('📊 测试结果统计:');
  console.log(`总测试数: ${testResults.total}`);
  console.log(`通过: ${testResults.passed} ✅`);
  console.log(`失败: ${testResults.failed} ❌`);
  console.log(`成功率: ${((testResults.passed / testResults.total) * 100).toFixed(2)}%`);

  if (testResults.errors.length > 0) {
    console.log('\n❌ 失败的测试:');
    testResults.errors.forEach(({ name, error }) => {
      console.log(`  - ${name}: ${error}`);
    });
  }

  console.log('\n🎉 后台管理系统功能测试完成!');
}

// 运行测试
if (require.main === module) {
  runAllTests().catch(console.error);
}

module.exports = {
  runAllTests,
  testResults
};
