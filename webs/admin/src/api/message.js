import {http} from '@/utils/request';

// 消息相关API
export const messageApi = {
  // 获取消息列表
  getMessages: params => http.get('/messages', params),

  // 创建消息
  createMessage: data => http.post('/messages', data),

  // 更新消息状态
  updateMessage: (id, data) => http.put(`/messages/${id}`, data),

  // 删除消息
  deleteMessage: id => http.delete(`/messages/${id}`),

  // 标记消息为已读
  markAsRead: id => http.put(`/messages/${id}`, {read: true}),

  // 获取留言统计
  getMessageStatistics: () => http.get('/messages/statistics'),

  // 批量标记为已读
  batchMarkAsRead: data => http.post('/messages/batch-read', data),

  // 批量删除留言
  batchDeleteMessages: data => http.post('/messages/batch-delete', data)
};

// 通知相关API
export const notificationApi = {
  // 获取通知列表
  getNotifications: params => http.get('/notifications', params),

  // 创建通知
  createNotification: data => http.post('/notifications', data),

  // 更新通知
  updateNotification: (id, data) => http.put(`/notifications/${id}`, data),

  // 删除通知
  deleteNotification: id => http.delete(`/notifications/${id}`),

  // 标记通知为已读
  markAsRead: id => http.put(`/notifications/${id}`, {read: true})
};
