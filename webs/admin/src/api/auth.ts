import { http } from '@/utils/request'
import type { LoginForm, ApiResponse, User } from '@/types'

// 登录
export const login = (data: LoginForm): Promise<ApiResponse<{ token: string; user: User }>> => {
  return http.post('/auth/login', data)
}

// 获取用户信息
export const getUserInfo = (): Promise<ApiResponse<User>> => {
  return http.get('/auth/userinfo')
}

// 退出登录
export const logout = (): Promise<ApiResponse> => {
  return http.post('/auth/logout')
}
