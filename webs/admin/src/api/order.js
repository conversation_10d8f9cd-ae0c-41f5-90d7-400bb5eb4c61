import {http} from '@/utils/request';

// 订单相关API
export const orderApi = {
  // 获取订单列表
  getOrders: params => http.get('/orders', params),

  // 获取今日订单
  getTodayOrders: () => http.get('/orders/today'),

  // 获取订单详情
  getOrderDetail: id => http.get(`/orders/${id}`),

  // 更新订单状态
  updateOrderStatus: (id, status) => http.put(`/orders/${id}`, {status}),

  // 删除订单
  deleteOrder: id => http.delete(`/orders/${id}`),

  // 获取订单统计
  getOrderStatistics: params => http.get('/orders/statistics', params),

  // 批量更新订单状态
  batchUpdateStatus: data => http.post('/orders/batch-status', data),

  // 导出订单
  exportOrders: params => http.get('/orders/export', params),

  // 获取订单分析数据
  getOrderAnalytics: params => http.get('/orders/analytics', params)
};
