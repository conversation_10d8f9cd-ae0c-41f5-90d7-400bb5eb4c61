<template>
  <div
    class="today-menu"
    v-motion
    :initial="{ opacity: 0, y: 20 }"
    :enter="{ opacity: 1, y: 0, transition: { duration: 600 } }"
  >
    <!-- 页面头部 -->
    <div class="page-header">
      <div class="header-content">
        <div class="header-info">
          <h1 class="page-title">今日菜单</h1>
          <p class="page-subtitle">
            设置和管理今日推荐菜品，为用户提供精选美食
          </p>
          <div class="date-info">
            <el-icon><Calendar /></el-icon>
            <span>{{ currentDate }}</span>
          </div>
        </div>
        <div class="header-stats">
          <div class="stat-card">
            <div class="stat-number">{{ selectedDishes.length }}</div>
            <div class="stat-label">已选菜品</div>
          </div>
          <div class="stat-card">
            <div class="stat-number">{{ totalPrice.toFixed(2) }}</div>
            <div class="stat-label">总价值(¥)</div>
          </div>
          <div class="stat-card">
            <div class="stat-number">{{ categoryCount }}</div>
            <div class="stat-label">涵盖分类</div>
          </div>
        </div>
      </div>
    </div>

    <!-- 操作栏 -->
    <div class="action-bar">
      <div class="action-left">
        <el-button type="primary" @click="showAddDialog = true" size="large">
          <el-icon><Plus /></el-icon>
          添加菜品
        </el-button>
        <el-button @click="handleSaveMenu" :loading="saveLoading" size="large">
          <el-icon><Check /></el-icon>
          保存菜单
        </el-button>
      </div>
      <div class="action-right">
        <el-button @click="handlePreview" size="large">
          <el-icon><View /></el-icon>
          预览菜单
        </el-button>
        <el-button @click="handleClearAll" type="danger" plain size="large">
          <el-icon><Delete /></el-icon>
          清空菜单
        </el-button>
      </div>
    </div>

    <!-- 菜单日期选择 -->
    <div class="date-selector">
      <el-date-picker
        v-model="selectedDate"
        type="date"
        placeholder="选择日期"
        format="YYYY-MM-DD"
        value-format="YYYY-MM-DD"
        @change="handleDateChange"
      />
      <el-tag v-if="isToday" type="success" class="today-tag">今天</el-tag>
    </div>

    <!-- 菜品选择区域 -->
    <div class="dish-selection">
      <el-row :gutter="20">
        <!-- 左侧：可选菜品 -->
        <el-col :span="12">
          <div class="selection-panel">
            <div class="panel-header">
              <h3>可选菜品</h3>
              <div class="search-box">
                <el-input
                  v-model="searchKeyword"
                  placeholder="搜索菜品"
                  clearable
                  @input="handleSearch"
                >
                  <template #prefix>
                    <el-icon><Search /></el-icon>
                  </template>
                </el-input>
              </div>
            </div>

            <div class="category-tabs">
              <el-tabs
                v-model="activeCategory"
                @tab-change="handleCategoryChange"
              >
                <el-tab-pane
                  v-for="category in categories"
                  :key="category.key"
                  :label="category.name"
                  :name="category.key"
                >
                  <div class="dish-grid">
                    <div
                      v-for="dish in filteredDishes"
                      :key="dish.id"
                      class="dish-card"
                      :class="{ 'dish-card--selected': isSelected(dish.id) }"
                      @click="toggleDish(dish)"
                    >
                      <div class="dish-image">
                        <el-image :src="dish.image" fit="cover" />
                        <div class="dish-overlay" v-if="isSelected(dish.id)">
                          <el-icon class="check-icon"><Check /></el-icon>
                        </div>
                      </div>
                      <div class="dish-info">
                        <h4 class="dish-name">{{ dish.name }}</h4>
                        <p class="dish-price">¥{{ dish.price }}</p>
                      </div>
                    </div>
                  </div>
                </el-tab-pane>
              </el-tabs>
            </div>
          </div>
        </el-col>

        <!-- 右侧：已选菜品 -->
        <el-col :span="12">
          <div class="selection-panel">
            <div class="panel-header">
              <h3>今日菜单 ({{ selectedDishes.length }})</h3>
              <el-button
                size="small"
                @click="clearSelection"
                v-if="selectedDishes.length"
              >
                清空选择
              </el-button>
            </div>

            <div class="selected-dishes">
              <div
                v-for="dish in selectedDishes"
                :key="dish.id"
                class="selected-dish"
              >
                <div class="dish-image">
                  <el-image :src="dish.image" fit="cover" />
                </div>
                <div class="dish-info">
                  <h4 class="dish-name">{{ dish.name }}</h4>
                  <p class="dish-category">
                    {{ getCategoryName(dish.category) }}
                  </p>
                  <p class="dish-price">¥{{ dish.price }}</p>
                </div>
                <div class="dish-actions">
                  <el-button
                    size="small"
                    type="danger"
                    circle
                    @click="removeDish(dish.id)"
                  >
                    <el-icon><Close /></el-icon>
                  </el-button>
                </div>
              </div>

              <div v-if="!selectedDishes.length" class="empty-state">
                <el-empty description="还没有选择菜品" />
              </div>
            </div>
          </div>
        </el-col>
      </el-row>
    </div>

    <!-- 菜单预览对话框 -->
    <el-dialog v-model="previewVisible" title="菜单预览" width="800px">
      <div class="menu-preview">
        <div class="preview-header">
          <h2>{{ selectedDate }} 菜单</h2>
          <p>共 {{ selectedDishes.length }} 道菜</p>
        </div>

        <div class="preview-categories">
          <div
            v-for="category in categoriesWithDishes"
            :key="category.key"
            class="preview-category"
          >
            <h3 class="category-title">{{ category.name }}</h3>
            <div class="category-dishes">
              <div
                v-for="dish in category.dishes"
                :key="dish.id"
                class="preview-dish"
              >
                <el-image
                  :src="dish.image"
                  class="preview-dish-image"
                  fit="cover"
                />
                <div class="preview-dish-info">
                  <h4>{{ dish.name }}</h4>
                  <p>¥{{ dish.price }}</p>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, reactive, computed, onMounted, watch } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { Check, View, Search, Close, Plus, Delete, Calendar } from '@element-plus/icons-vue'
import { dishApi } from '@/api/menu'
import { menuApi } from '@/api/menu'
import dayjs from 'dayjs'

const selectedDate = ref(dayjs().format('YYYY-MM-DD'))
const activeCategory = ref('hot')
const searchKeyword = ref('')
const selectedDishes = ref([])
const allDishes = ref([])
const saveLoading = ref(false)
const previewVisible = ref(false)

const categories = ref([
  { key: 'hot', name: '热菜' },
  { key: 'cold', name: '凉菜' },
  { key: 'soup', name: '汤品' },
  { key: 'staple', name: '主食' },
  { key: 'dessert', name: '甜品' }
])

// 计算属性
const currentDate = computed(() => {
  return dayjs().format('YYYY年MM月DD日 dddd')
})

const totalPrice = computed(() => {
  return selectedDishes.value.reduce((sum, dish) => sum + dish.price, 0)
})

const categoryCount = computed(() => {
  const categories = new Set(selectedDishes.value.map(dish => dish.category))
  return categories.size
})

const isToday = computed(() => {
  return selectedDate.value === dayjs().format('YYYY-MM-DD')
})

const filteredDishes = computed(() => {
  let dishes = allDishes.value.filter(dish => dish.category === activeCategory.value)

  if (searchKeyword.value) {
    dishes = dishes.filter(dish =>
      dish.name.toLowerCase().includes(searchKeyword.value.toLowerCase())
    )
  }

  return dishes
})

const categoriesWithDishes = computed(() => {
  return categories.value.map(category => ({
    ...category,
    dishes: selectedDishes.value.filter(dish => dish.category === category.key)
  })).filter(category => category.dishes.length > 0)
})

// 方法
const loadDishes = async () => {
  try {
    const res = await dishApi.getDishes({ status: 1, size: 1000 })
    if (res.data) {
      allDishes.value = res.data.list || []
    }
  } catch (error) {
    console.error('加载菜品失败:', error)
    // 使用模拟数据
    allDishes.value = [
      {
        id: 1,
        name: '红烧肉',
        category: 'hot',
        price: 28.00,
        image: 'https://via.placeholder.com/200x150/FF6B6B/FFFFFF?text=红烧肉',
        status: 1
      },
      {
        id: 2,
        name: '宫保鸡丁',
        category: 'hot',
        price: 22.00,
        image: 'https://via.placeholder.com/200x150/4ECDC4/FFFFFF?text=宫保鸡丁',
        status: 1
      },
      {
        id: 3,
        name: '麻婆豆腐',
        category: 'hot',
        price: 18.00,
        image: 'https://via.placeholder.com/200x150/45B7D1/FFFFFF?text=麻婆豆腐',
        status: 1
      },
      {
        id: 4,
        name: '凉拌黄瓜',
        category: 'cold',
        price: 12.00,
        image: 'https://via.placeholder.com/200x150/96CEB4/FFFFFF?text=凉拌黄瓜',
        status: 1
      },
      {
        id: 5,
        name: '口水鸡',
        category: 'cold',
        price: 25.00,
        image: 'https://via.placeholder.com/200x150/FFEAA7/000000?text=口水鸡',
        status: 1
      },
      {
        id: 6,
        name: '紫菜蛋花汤',
        category: 'soup',
        price: 15.00,
        image: 'https://via.placeholder.com/200x150/DDA0DD/FFFFFF?text=紫菜蛋花汤',
        status: 1
      },
      {
        id: 7,
        name: '白米饭',
        category: 'staple',
        price: 3.00,
        image: 'https://via.placeholder.com/200x150/F0F0F0/000000?text=白米饭',
        status: 1
      },
      {
        id: 8,
        name: '红豆汤',
        category: 'dessert',
        price: 8.00,
        image: 'https://via.placeholder.com/200x150/FF7675/FFFFFF?text=红豆汤',
        status: 1
      }
    ]
  }
}

const loadTodayMenu = async () => {
  try {
    const res = await menuApi.getTodayMenu()
    if (res.data && res.data.dishes) {
      selectedDishes.value = res.data.dishes
    }
  } catch (error) {
    console.error('加载今日菜单失败:', error)
    // 不显示错误，可能是还没有设置今日菜单
  }
}

const isSelected = (dishId) => {
  return selectedDishes.value.some(dish => dish.id === dishId)
}

const toggleDish = (dish) => {
  const index = selectedDishes.value.findIndex(d => d.id === dish.id)
  if (index > -1) {
    selectedDishes.value.splice(index, 1)
  } else {
    selectedDishes.value.push(dish)
  }
}

const removeDish = (dishId) => {
  const index = selectedDishes.value.findIndex(d => d.id === dishId)
  if (index > -1) {
    selectedDishes.value.splice(index, 1)
  }
}

const clearSelection = async () => {
  try {
    await ElMessageBox.confirm('确定要清空所有选择的菜品吗？', '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    })
    selectedDishes.value = []
  } catch (error) {
    // 用户取消
  }
}

const getCategoryName = (categoryKey) => {
  const category = categories.value.find(c => c.key === categoryKey)
  return category ? category.name : categoryKey
}

const handleDateChange = (date) => {
  // 根据日期加载对应的菜单
  if (date === dayjs().format('YYYY-MM-DD')) {
    loadTodayMenu()
  } else {
    // 加载历史菜单或清空选择
    selectedDishes.value = []
  }
}

const handleSearch = () => {
  // 搜索逻辑已在计算属性中处理
}

const handleCategoryChange = (categoryKey) => {
  activeCategory.value = categoryKey
}

const handleSaveMenu = async () => {
  if (!selectedDishes.value.length) {
    ElMessage.warning('请至少选择一道菜品')
    return
  }

  try {
    saveLoading.value = true

    const menuData = {
      date: selectedDate.value,
      dishes: selectedDishes.value.map(dish => ({
        dishId: dish.id,
        dishName: dish.name,
        category: dish.category,
        price: dish.price,
        image: dish.image
      }))
    }

    await menuApi.createMenu(menuData)
    ElMessage.success('菜单保存成功')
  } catch (error) {
    console.error('保存菜单失败:', error)
    ElMessage.error('保存菜单失败')
  } finally {
    saveLoading.value = false
  }
}

const handlePreview = () => {
  if (!selectedDishes.value.length) {
    ElMessage.warning('请先选择菜品')
    return
  }
  previewVisible.value = true
}

const handleClearAll = async () => {
  if (!selectedDishes.value.length) {
    ElMessage.warning('当前没有选择的菜品')
    return
  }

  try {
    await ElMessageBox.confirm('确定要清空所有选择的菜品吗？', '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    })
    selectedDishes.value = []
    ElMessage.success('已清空菜单')
  } catch (error) {
    // 用户取消
  }
}

onMounted(() => {
  loadDishes()
  loadTodayMenu()
})
</script>

<style scoped lang="scss">
.today-menu {
  padding: 24px;
  background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
  min-height: calc(100vh - 60px);
}

.page-header {
  background: white;
  border-radius: 16px;
  padding: 32px;
  margin-bottom: 24px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);

  .header-content {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;

    .header-info {
      flex: 1;

      .page-title {
        font-size: 28px;
        font-weight: 700;
        color: #1a202c;
        margin: 0 0 8px 0;
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
      }

      .page-subtitle {
        font-size: 16px;
        color: #718096;
        margin: 0 0 16px 0;
        line-height: 1.6;
      }

      .date-info {
        display: flex;
        align-items: center;
        gap: 8px;
        color: #4a5568;
        font-size: 14px;

        .el-icon {
          color: #667eea;
        }
      }
    }

    .header-stats {
      display: flex;
      gap: 24px;

      .stat-card {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        padding: 20px;
        border-radius: 12px;
        text-align: center;
        min-width: 100px;
        box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3);

        .stat-number {
          font-size: 24px;
          font-weight: 700;
          margin-bottom: 4px;
        }

        .stat-label {
          font-size: 12px;
          opacity: 0.9;
        }
      }
    }
  }
}

.action-bar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  background: white;
  padding: 20px 32px;
  border-radius: 12px;
  margin-bottom: 24px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);

  .action-left,
  .action-right {
    display: flex;
    gap: 12px;
  }
}

.date-selector {
  @apply flex items-center space-x-3 mb-6;

  .today-tag {
    @apply ml-2;
  }
}

.dish-selection {
  .selection-panel {
    @apply bg-white rounded-lg shadow-sm h-full;

    .panel-header {
      @apply flex justify-between items-center p-4 border-b border-gray-200;

      h3 {
        @apply text-lg font-semibold text-gray-900;
      }

      .search-box {
        @apply w-64;
      }
    }

    .category-tabs {
      @apply p-4;

      :deep(.el-tabs__header) {
        @apply mb-4;
      }
    }
  }
}

.dish-grid {
  @apply grid grid-cols-2 gap-4 max-h-96 overflow-y-auto;
}

.dish-card {
  @apply border border-gray-200 rounded-lg overflow-hidden cursor-pointer transition-all duration-200 hover:shadow-md;

  &--selected {
    @apply border-blue-500 bg-blue-50;
  }

  .dish-image {
    @apply relative h-24;

    .dish-overlay {
      @apply absolute inset-0 bg-blue-500 bg-opacity-50 flex items-center justify-center;

      .check-icon {
        @apply text-white text-xl;
      }
    }
  }

  .dish-info {
    @apply p-3;

    .dish-name {
      @apply text-sm font-medium text-gray-900 mb-1;
    }

    .dish-price {
      @apply text-sm text-blue-600 font-semibold;
    }
  }
}

.selected-dishes {
  @apply p-4 max-h-96 overflow-y-auto;
}

.selected-dish {
  @apply flex items-center space-x-3 p-3 border border-gray-200 rounded-lg mb-3;

  .dish-image {
    @apply w-16 h-16 rounded-lg overflow-hidden flex-shrink-0;
  }

  .dish-info {
    @apply flex-1;

    .dish-name {
      @apply text-sm font-medium text-gray-900 mb-1;
    }

    .dish-category {
      @apply text-xs text-gray-500 mb-1;
    }

    .dish-price {
      @apply text-sm text-blue-600 font-semibold;
    }
  }

  .dish-actions {
    @apply flex-shrink-0;
  }
}

.empty-state {
  @apply py-8;
}

.menu-preview {
  .preview-header {
    @apply text-center mb-6 pb-4 border-b border-gray-200;

    h2 {
      @apply text-xl font-bold text-gray-900 mb-2;
    }

    p {
      @apply text-gray-600;
    }
  }

  .preview-category {
    @apply mb-6;

    .category-title {
      @apply text-lg font-semibold text-gray-900 mb-3;
    }

    .category-dishes {
      @apply grid grid-cols-3 gap-4;
    }
  }
}

.preview-dish {
  @apply border border-gray-200 rounded-lg overflow-hidden;

  .preview-dish-image {
    @apply w-full h-20;
  }

  .preview-dish-info {
    @apply p-3;

    h4 {
      @apply text-sm font-medium text-gray-900 mb-1;
    }

    p {
      @apply text-sm text-blue-600 font-semibold;
    }
  }
}
</style>
