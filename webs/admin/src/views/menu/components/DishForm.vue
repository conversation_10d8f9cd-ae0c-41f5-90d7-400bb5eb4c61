<template>
  <div class="dish-form">
    <el-form ref="formRef" :model="form" :rules="rules" label-width="100px">
      <el-form-item label="菜品名称" prop="name">
        <el-input v-model="form.name" placeholder="请输入菜品名称" />
      </el-form-item>

      <el-form-item label="分类" prop="category">
        <el-select
          v-model="form.category"
          placeholder="请选择分类"
          style="width: 100%"
        >
          <el-option
            v-for="category in categories"
            :key="category"
            :label="category"
            :value="category"
          />
        </el-select>
      </el-form-item>

      <el-form-item label="描述" prop="description">
        <el-input
          v-model="form.description"
          type="textarea"
          :rows="3"
          placeholder="请输入菜品描述"
        />
      </el-form-item>

      <el-form-item label="食材" prop="ingredients">
        <el-input
          v-model="form.ingredients"
          type="textarea"
          :rows="2"
          placeholder="请输入食材，用逗号分隔"
        />
      </el-form-item>

      <el-form-item label="制作方法" prop="cookingMethod">
        <el-input
          v-model="form.cookingMethod"
          type="textarea"
          :rows="4"
          placeholder="请输入制作方法"
        />
      </el-form-item>

      <el-form-item label="菜品图片">
        <el-upload
          class="dish-uploader"
          :action="uploadUrl"
          :headers="uploadHeaders"
          :show-file-list="false"
          :on-success="handleUploadSuccess"
          :before-upload="beforeUpload"
        >
          <img v-if="form.image" :src="form.image" class="dish-image" />
          <el-icon v-else class="dish-uploader-icon"><Plus /></el-icon>
        </el-upload>
      </el-form-item>

      <el-form-item label="是否可用">
        <el-switch
          v-model="form.isAvailable"
          active-text="可用"
          inactive-text="不可用"
        />
      </el-form-item>

      <div class="form-actions">
        <el-button @click="handleCancel">取消</el-button>
        <el-button type="primary" @click="handleSubmit">
          {{ isEdit ? "更新" : "创建" }}
        </el-button>
      </div>
    </el-form>
  </div>
</template>

<script setup>
import { ref, reactive, watch, computed } from "vue";
import { ElMessage } from "element-plus";
import { Plus } from "@element-plus/icons-vue";
import { useUserStore } from "@/stores/user";

const props = defineProps({
  formData: {
    type: Object,
    default: () => ({})
  },
  isEdit: {
    type: Boolean,
    default: false
  }
});

const emit = defineEmits(["submit", "cancel"]);
const formRef = ref();
const userStore = useUserStore();

const form = reactive({
  name: "",
  category: "",
  description: "",
  ingredients: "",
  cookingMethod: "",
  image: "",
  isAvailable: true
});

const rules = {
  name: [{ required: true, message: "请输入菜品名称", trigger: "blur" }],
  category: [{ required: true, message: "请选择分类", trigger: "change" }],
  description: [{ required: true, message: "请输入菜品描述", trigger: "blur" }]
};

// 分类选项
const categories = ["热菜", "凉菜", "汤品", "主食", "甜品"];

// 上传地址
const uploadUrl = "/api/upload";

// 上传请求头
const uploadHeaders = computed(() => ({
  Authorization: `Bearer ${userStore.token}`
}));

// 监听表单数据变化
watch(
  () => props.formData,
  newData => {
    if (newData && Object.keys(newData).length > 0) {
      Object.assign(form, {
        name: newData.name || "",
        category: newData.category || "",
        description: newData.description || "",
        ingredients: newData.ingredients || "",
        cookingMethod: newData.cookingMethod || "",
        image: newData.image || "",
        isAvailable:
          newData.isAvailable !== undefined ? newData.isAvailable : true
      });
    }
  },
  { immediate: true }
);

// 上传前验证
const beforeUpload = file => {
  const isJPG = file.type === "image/jpeg" || file.type === "image/png";
  const isLt2M = file.size / 1024 / 1024 < 2;

  if (!isJPG) {
    ElMessage.error("上传图片只能是 JPG/PNG 格式!");
    return false;
  }
  if (!isLt2M) {
    ElMessage.error("上传图片大小不能超过 2MB!");
    return false;
  }
  return true;
};

// 上传成功
const handleUploadSuccess = response => {
  if (response.code === 200) {
    form.image = response.data.url;
    ElMessage.success("图片上传成功");
  } else {
    ElMessage.error("图片上传失败");
  }
};

// 提交表单
const handleSubmit = async () => {
  if (!formRef.value) return;

  try {
    const valid = await formRef.value.validate();
    if (!valid) return;

    emit("submit", { ...form });
  } catch (error) {
    console.error("表单验证失败:", error);
  }
};

// 取消
const handleCancel = () => {
  emit("cancel");
};

// 重置表单
const resetForm = () => {
  if (formRef.value) {
    formRef.value.resetFields();
  }
  Object.assign(form, {
    name: "",
    category: "",
    description: "",
    ingredients: "",
    cookingMethod: "",
    image: "",
    isAvailable: true
  });
};

// 暴露方法给父组件
defineExpose({
  resetForm
});
</script>

<style scoped lang="scss">
.dish-form {
  padding: 20px;

  .form-actions {
    display: flex;
    justify-content: flex-end;
    gap: 12px;
    margin-top: 30px;
    padding-top: 20px;
    border-top: 1px solid #ebeef5;
  }
}

.dish-uploader {
  :deep(.el-upload) {
    border: 2px dashed #d9d9d9;
    border-radius: 8px;
    cursor: pointer;
    position: relative;
    overflow: hidden;
    transition: all 0.3s ease;

    &:hover {
      border-color: #409eff;
      background-color: #f5f7fa;
    }
  }
}

.dish-uploader-icon {
  font-size: 32px;
  color: #8c939d;
  width: 160px;
  height: 120px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: #fafafa;
  transition: color 0.3s ease;

  &:hover {
    color: #409eff;
  }
}

.dish-image {
  width: 160px;
  height: 120px;
  display: block;
  object-fit: cover;
  border-radius: 6px;
}

// 响应式设计
@media (max-width: 768px) {
  .dish-form {
    padding: 16px;

    .form-actions {
      flex-direction: column;

      .el-button {
        width: 100%;
      }
    }
  }

  .dish-uploader-icon,
  .dish-image {
    width: 120px;
    height: 90px;
  }
}

// 表单项优化
:deep(.el-form-item) {
  margin-bottom: 20px;

  .el-form-item__label {
    font-weight: 500;
    color: #333;
  }

  .el-input__wrapper,
  .el-textarea__inner {
    border-radius: 8px;
    transition: all 0.3s ease;

    &:hover {
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    }

    &.is-focus {
      box-shadow: 0 2px 12px rgba(64, 158, 255, 0.2);
    }
  }

  .el-select {
    width: 100%;
  }
}

// 开关样式优化
:deep(.el-switch) {
  .el-switch__core {
    border-radius: 12px;
  }
}
</style>
