<template>
  <div class="today-orders">
    <CustomTable
      title="今日订单"
      :data="tableData"
      :columns="columns"
      :loading="loading"
      :show-search="false"
    >
      <template #status="{ row }">
        <el-tag :type="getStatusType(row.status)">
          {{ getStatusText(row.status) }}
        </el-tag>
      </template>
      
      <template #actions="{ row }">
        <el-button size="small" @click="handleView(row)">查看</el-button>
        <el-button size="small" type="primary" @click="handleComplete(row)">完成</el-button>
      </template>
    </CustomTable>
  </div>
</template>

<script>
import { defineComponent, ref, onMounted } from 'vue'
import { ElMessage } from 'element-plus'
import CustomTable from '@/components/CustomTable.vue'
import { orderApi } from '@/api/order'

export default defineComponent({
  name: 'TodayOrders',
  components: {
    CustomTable
  },
  setup() {
    const loading = ref(false)
    const tableData = ref([])
    
    const columns = [
      { prop: 'id', label: '订单号', width: 100 },
      { prop: 'userName', label: '用户' },
      { prop: 'items', label: '菜品数量' },
      { prop: 'status', label: '状态', slot: 'status' },
      { prop: 'mealTime', label: '用餐时间' }
    ]
    
    const loadData = async () => {
      loading.value = true
      try {
        const response = await orderApi.getTodayOrders()
        if (response.data) {
          tableData.value = response.data
        }
      } catch (error) {
        console.error('加载今日订单失败:', error)
        ElMessage.error('加载数据失败')
      } finally {
        loading.value = false
      }
    }
    
    const getStatusType = (status) => {
      const statusMap = {
        pending: 'warning',
        completed: 'success',
        cancelled: 'danger'
      }
      return statusMap[status] || 'info'
    }
    
    const getStatusText = (status) => {
      const statusMap = {
        pending: '待处理',
        completed: '已完成',
        cancelled: '已取消'
      }
      return statusMap[status] || '未知'
    }
    
    const handleView = (row) => {
      ElMessage.info(`查看订单: ${row.id}`)
    }
    
    const handleComplete = (row) => {
      ElMessage.success(`订单 ${row.id} 已完成`)
    }
    
    onMounted(() => {
      loadData()
    })
    
    return {
      loading,
      tableData,
      columns,
      getStatusType,
      getStatusText,
      handleView,
      handleComplete
    }
  }
})
</script>
