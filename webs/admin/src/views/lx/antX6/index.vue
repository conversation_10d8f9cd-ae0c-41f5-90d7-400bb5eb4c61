<template>
  <div class="w-full h-full">
    <div id="containers" />
    <TeleportContainer />
  </div>
</template>

<script setup>
import { ref, onMounted } from "vue";
import { Graph } from "@antv/x6";
import { register, getTeleport } from "@antv/x6-vue-shape";
import Node from "./node.vue";

const TeleportContainer = getTeleport();
const graph = ref(null);

// 创建节点 - 创建节点并指定位置
const createNode = (params = {}) => {
  if (!graph.value) return;
  const { id, x, y, label = "" } = params;

  // 注册 Vue 组件节点
  register({
    shape: "custom-vue-node",
    width: 120,
    height: 68,
    component: Node
  });

  // 创建节点
  const node = graph.value.addNode({
    shape: "custom-vue-node", // 这里使用 Vue 自定义节点
    id,
    x,
    y,
    data: { label } // 传递 label 到 data 中
  });

  return node;
};

// 创建边 - 标签2种圆和文字
const createEdge = (params = {}) => {
  const { source, target, label = "50%", ...rest } = params;
  if (!graph.value) return;
  const edge = graph.value.addEdge({
    shape: "edge",
    source,
    target,
    zIndex: 10, // 提升线的层级，使其高于节点
    router: { name: "orth" }, // 使用 orth 路由，使线转弯为直角
    attrs: {
      line: {
        stroke: "#86909C", // 自定义边线颜色
        strokeWidth: 2,
        targetMarker: {
          name: "classic", // 箭头样式
          size: 8,
          stroke: "#86909C",
          fill: "#86909C"
        }
      }
    },
    labels: [
      {
        attrs: {
          label: {
            text: label,
            fill: "#000", // 标签颜色
            fontSize: 12
          }
        }
      }
    ],
    ...rest
  });

  return edge;
};

// 初始化图形
const initGraph = () => {
  graph.value = new Graph({
    container: document.getElementById("containers"),
    autoResize: true,
    grid: true,
    background: {
      color: "#F2F7FA"
    },
    connecting: {
      router: "orth", // 使边线转弯为直角
      connector: "rounded" // 圆角连接线
    }
  });
};

// 手动布局
const manualLayout = () => {
  // 创建节点，手动设置位置
  const nodes = [
    createNode({ id: "n1", x: 100, y: 100, label: "数据资源1" }),
    createNode({ id: "n2", x: 300, y: 100, label: "数据资源2" }),
    createNode({ id: "n3", x: 500, y: 100, label: "数据资源3" }),
    createNode({ id: "n4", x: 100, y: 250, label: "数据资源4" }),
    createNode({ id: "n5", x: 100, y: 400, label: "数据资源5" }),
    createNode({ id: "n6", x: 300, y: 400, label: "数据资源6" }),
    createNode({ id: "n7", x: 100, y: 550, label: "数据资源7" }),
    createNode({ id: "n8", x: 500, y: 550, label: "数据资源8" }),
    createNode({ id: "p1", x: 100, y: 700, label: "产品: PX-00" }),
    createNode({ id: "p2", x: 300, y: 700, label: "产品: PX-00" })
  ];

  // 创建边
  // 创建边
  createEdge({
    source: "n2",
    target: "n4",
    label: "50%",
    vertices: [
      // { x: 300, y: 150 }, // 先向下
      // { x: 100, y: 150 } // 然后水平到 n4
    ]
  });

  createEdge({
    source: "n3",
    target: "n4",
    label: "50%",
    vertices: [
      // { x: 500, y: 150 }, // 先向下
      // { x: 100, y: 150 } // 然后水平到 n4
    ]
  });

  createEdge({
    source: "n4",
    target: "n6",
    label: "50%",
    vertices: [
      // { x: 200, y: 400 } // 从 n4 的右侧出发
    ]
  });
  //
  createEdge({ source: "n1", target: "n4", label: "100%" });
  // createEdge({ source: "n2", target: "n4", label: "50%" });
  // createEdge({ source: "n3", target: "n4", label: "50%" });
  createEdge({ source: "n3", target: "n8", label: "50%" });
  createEdge({ source: "n4", target: "n5", label: "50%" });
  createEdge({ source: "n4", target: "n6", label: "50%" });
  createEdge({ source: "n5", target: "n7", label: "100%" });
  // createEdge({ source: "n6", target: "n7", label: "100%" });
  createEdge({ source: "n7", target: "p1", label: "30%" });
  createEdge({ source: "n7", target: "p2", label: "30%" });
  createEdge({ source: "n8", target: "p1", label: "30%" });
  createEdge({ source: "n8", target: "p2", label: "30%" });
};

// 组件挂载时初始化
onMounted(() => {
  initGraph();
  manualLayout(); // 手动布局
});
</script>

<style scoped>
/* 你的样式可以在这里修改 */
</style>
