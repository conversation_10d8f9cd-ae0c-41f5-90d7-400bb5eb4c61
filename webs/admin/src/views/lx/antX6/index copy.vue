<template>
  <div class="w-full h-full">
    <div id="containers" />
    <TeleportContainer />
  </div>
</template>

<script setup>
import { ref, onMounted } from "vue";
import { Graph } from "@antv/x6";
import { register, getTeleport } from "@antv/x6-vue-shape";
import Node from "./node.vue";

const TeleportContainer = getTeleport();
const graph = ref(null);
// 创建节点-创建节点3种
const createNode = (params = {}) => {
  if (!graph.value) return;
  const { type = "", shape = "custom-vue-node-a" } = params;
  register({
    shape,
    width: 120,
    height: 68,
    component: Node
  });
  const nodes = {
    shape,
    id: shape
  };
  graph.value.addNode(nodes);
  return nodes;
};
// 边 -标签2种圆和文字
const createEdge = (params = {}) => {
  const { source, target } = params;
  if (!graph.value) return;
  const nodes = {
    shape: "edge",
    source,
    target, //id
    vertices: [],
    labels: [
      {
        attrs: {
          label: {
            text: "50%"
          }
        }
      }
    ],
    // router: "orth", //边的每一条链接线段都是水平或垂直的
    // connector: "rounded", //接器将连线之间的倒角处理为圆弧倒角
    attrs: {
      // 设置箭头线颜色
      line: {
        stroke: "#86909C" // 使用自定义边框色
      }
    }
  };
  graph.value.addEdge(nodes);
  return nodes;
};
// 初始化
const initGraph = () => {
  graph.value = new Graph({
    container: document.getElementById("containers"),
    // width: 1000,
    // height: 600,
    autoResize: true,
    grid: true,
    background: {
      color: "#F2F7FA"
    }
  });
};

//布局
const createLayout = () => {
  const nodes = [
    // 写死点
    createNode({ shape: "a1" }),
    createNode({ shape: "a2" }),
    createNode({ shape: "a3" }),
    createNode({ shape: "b1" }),
    createNode({ shape: "c1" }),
    createNode({ shape: "c2" }),
    createNode({ shape: "d1" }),
    createNode({ shape: "d3" }),
    createNode({ shape: "e1" }),
    createNode({ shape: "e2" })
  ];
  const edges = [
    createEdge({ source: "a1", target: "b1" }),
    createEdge({ source: "a2", target: "b1" }),
    createEdge({ source: "a3", target: "b1" }),
    createEdge({ source: "a3", target: "d3" }),
    createEdge({ source: "b1", target: "c1" }),
    createEdge({ source: "b1", target: "c2" }),
    createEdge({ source: "c1", target: "d1" }),
    createEdge({ source: "c2", target: "d1" }),
    createEdge({ source: "d1", target: "e1" }),
    createEdge({ source: "d1", target: "e2" }),
    createEdge({ source: "d3", target: "e2" })
  ];

  // 构建节点分组
  const nodeGroups = {};
  edges.forEach(edge => {
    if (!nodeGroups[edge.source]) {
      nodeGroups[edge.source] = [];
    }
    if (!nodeGroups[edge.target]) {
      nodeGroups[edge.target] = [];
    }
    nodeGroups[edge.source].push(edge.target);
  });

  // 找到没有被指向的节点
  const rootNodes = nodes.filter(node => !nodeGroups[node.id]);

  // 构建网格布局数据
  const gridData = {
    nodes: [],
    edges: []
  };

  // 将没有被指向的节点放置在第一层
  let currentRow = 0;
  rootNodes.forEach(node => {
    gridData.nodes.push({
      id: node.id,
      x: 100 + currentRow * 150,
      y: 100
    });
    currentRow++;
  });

  // 将其他节点按照分组放置
  for (const sourceNode in nodeGroups) {
    const targetNodes = nodeGroups[sourceNode];
    currentRow = 0;
    targetNodes.forEach(targetNode => {
      gridData.nodes.push({
        id: targetNode,
        x: 100 + currentRow * 150,
        y: 100 + (currentRow % 2 === 0 ? 100 : 150)
      });
      currentRow++;
    });
  }

  // 更新节点位置
  gridData.nodes.forEach(node => {
    const cell = graph.value.getCellById(node.id);
    if (cell) {
      cell.setPosition(node.x, node.y);
    }
  });
};
onMounted(() => {
  initGraph();
  createLayout();
});
</script>

<style lang="scss" scoped></style>
