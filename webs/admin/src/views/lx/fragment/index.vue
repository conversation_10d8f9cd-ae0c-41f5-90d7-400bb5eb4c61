<template>
  <div>
    <!--    <OneDemo />-->
    <!--    <Grid />-->
    <Three />
  </div>
</template>

<script setup lang="ts">
// import OneDemo from "./onther/oneDemo.vue";
// import Grid from "@/views/lx/fragment/onther/grid.vue";
// import GsapDemo from "@/views/lx/fragment/onther/gsapDemo.vue";
import Three from "./onther/three.vue";
defineOptions({
  name: "Fragment"
});
</script>

<style scoped></style>
