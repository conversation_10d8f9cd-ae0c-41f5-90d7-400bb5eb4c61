<template>
  <div class="analytics-overview">
    <!-- 核心指标卡片 -->
    <el-row :gutter="20" class="stats-row">
      <el-col
        :xs="24"
        :sm="12"
        :md="6"
        v-for="(stat, index) in coreStats"
        :key="index"
      >
        <StatsCard
          :title="stat.title"
          :value="stat.value"
          :icon="stat.icon"
          :type="stat.type"
          :trend="stat.trend"
          :description="stat.description"
          :animated="true"
        />
      </el-col>
    </el-row>

    <!-- 图表区域 -->
    <el-row :gutter="20" class="charts-row">
      <!-- 订单趋势图 -->
      <el-col :xs="24" :lg="12">
        <div class="chart-card">
          <div class="chart-header">
            <h3>订单趋势</h3>
            <el-radio-group
              v-model="orderPeriod"
              size="small"
              @change="updateOrderChart"
            >
              <el-radio-button label="7d">7天</el-radio-button>
              <el-radio-button label="30d">30天</el-radio-button>
              <el-radio-button label="90d">90天</el-radio-button>
            </el-radio-group>
          </div>
          <div ref="orderChartRef" class="chart-container"></div>
        </div>
      </el-col>

      <!-- 菜品分类分布 -->
      <el-col :xs="24" :lg="12">
        <div class="chart-card">
          <div class="chart-header">
            <h3>菜品分类分布</h3>
          </div>
          <div ref="categoryChartRef" class="chart-container"></div>
        </div>
      </el-col>
    </el-row>

    <!-- 热门菜品和用户活跃度 -->
    <el-row :gutter="20" class="data-row">
      <!-- 热门菜品排行 -->
      <el-col :xs="24" :lg="12">
        <div class="data-card">
          <div class="card-header">
            <h3>热门菜品排行</h3>
            <el-button size="small" @click="refreshHotDishes">
              <el-icon><Refresh /></el-icon>
              刷新
            </el-button>
          </div>
          <div class="hot-dishes-list">
            <div
              v-for="(dish, index) in hotDishes"
              :key="dish.id"
              class="dish-item"
            >
              <div class="dish-rank">{{ index + 1 }}</div>
              <div class="dish-image">
                <el-image :src="dish.image" fit="cover" />
              </div>
              <div class="dish-info">
                <h4 class="dish-name">{{ dish.name }}</h4>
                <p class="dish-category">{{ dish.category }}</p>
              </div>
              <div class="dish-stats">
                <div class="order-count">{{ dish.orderCount }}次</div>
                <div class="revenue">¥{{ dish.revenue }}</div>
              </div>
            </div>
          </div>
        </div>
      </el-col>

      <!-- 用户活跃度 -->
      <el-col :xs="24" :lg="12">
        <div class="data-card">
          <div class="card-header">
            <h3>用户活跃度</h3>
            <el-button size="small" @click="refreshUserActivity">
              <el-icon><Refresh /></el-icon>
              刷新
            </el-button>
          </div>
          <div class="activity-chart">
            <div ref="activityChartRef" class="chart-container"></div>
          </div>
        </div>
      </el-col>
    </el-row>

    <!-- 实时数据 -->
    <el-row :gutter="20" class="realtime-row">
      <el-col :span="24">
        <div class="realtime-card">
          <div class="card-header">
            <h3>实时数据</h3>
            <div class="realtime-status">
              <el-tag
                :type="realtimeStatus ? 'success' : 'danger'"
                size="small"
              >
                {{ realtimeStatus ? '在线' : '离线' }}
              </el-tag>
              <span class="update-time">最后更新: {{ lastUpdateTime }}</span>
            </div>
          </div>

          <el-row :gutter="20" class="realtime-stats">
            <el-col
              :xs="12"
              :sm="6"
              v-for="(item, index) in realtimeData"
              :key="index"
            >
              <div class="realtime-item">
                <div class="item-icon" :class="`item-icon--${item.type}`">
                  <component :is="item.icon" />
                </div>
                <div class="item-info">
                  <div class="item-value">{{ item.value }}</div>
                  <div class="item-label">{{ item.label }}</div>
                </div>
              </div>
            </el-col>
          </el-row>
        </div>
      </el-col>
    </el-row>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted, nextTick, onUnmounted } from 'vue'
import * as echarts from 'echarts'
import { ElMessage } from 'element-plus'
import { Refresh, ShoppingCart, Bowl, User, TrendCharts, Clock, View } from '@element-plus/icons-vue'
import StatsCard from '@/components/StatsCard.vue'
import { menuApi } from '@/api/menu'
import { orderApi } from '@/api/order'
import { userApi } from '@/api/user'
import dayjs from 'dayjs'

const orderChartRef = ref()
const categoryChartRef = ref()
const activityChartRef = ref()
const orderPeriod = ref('7d')
const realtimeStatus = ref(true)
const lastUpdateTime = ref(dayjs().format('HH:mm:ss'))

let orderChart = null
let categoryChart = null
let activityChart = null
let realtimeTimer = null

// 核心统计数据
const coreStats = reactive([
  {
    title: '今日订单',
    value: 0,
    icon: ShoppingCart,
    type: 'primary',
    trend: '+12%',
    description: '较昨日'
  },
  {
    title: '菜品总数',
    value: 0,
    icon: Bowl,
    type: 'success',
    trend: '+5%',
    description: '较上周'
  },
  {
    title: '活跃用户',
    value: 0,
    icon: User,
    type: 'warning',
    trend: '+8%',
    description: '较上月'
  },
  {
    title: '月访问量',
    value: 0,
    icon: TrendCharts,
    type: 'danger',
    trend: '+15%',
    description: '较上月'
  }
])

// 热门菜品数据
const hotDishes = ref([])

// 实时数据
const realtimeData = reactive([
  {
    label: '在线用户',
    value: 0,
    icon: User,
    type: 'primary'
  },
  {
    label: '今日订单',
    value: 0,
    icon: ShoppingCart,
    type: 'success'
  },
  {
    label: '待处理',
    value: 0,
    icon: Clock,
    type: 'warning'
  },
  {
    label: '页面浏览',
    value: 0,
    icon: View,
    type: 'info'
  }
])

// 方法
const loadCoreStats = async () => {
  try {
    const [menuStats, orderStats, userStats] = await Promise.all([
      menuApi.getStatistics(),
      orderApi.getOrderStatistics(),
      userApi.getUserStatistics()
    ])

    if (menuStats.data) {
      coreStats[0].value = menuStats.data.todayOrders || 28
      coreStats[1].value = menuStats.data.totalDishes || 156
      coreStats[2].value = menuStats.data.activeUsers || 1240
      coreStats[3].value = menuStats.data.monthlyVisits || 8650
    }
  } catch (error) {
    console.error('加载核心统计数据失败:', error)
    // 使用模拟数据
    coreStats[0].value = 28
    coreStats[1].value = 156
    coreStats[2].value = 1240
    coreStats[3].value = 8650
  }
}

const loadHotDishes = async () => {
  try {
    // 这里应该调用获取热门菜品的API
    // const res = await dishApi.getHotDishes()

    // 使用模拟数据
    hotDishes.value = [
      {
        id: 1,
        name: '红烧肉',
        category: '热菜',
        image: 'https://picsum.photos/60/60?random=1',
        orderCount: 156,
        revenue: 4368
      },
      {
        id: 2,
        name: '宫保鸡丁',
        category: '热菜',
        image: 'https://picsum.photos/60/60?random=2',
        orderCount: 142,
        revenue: 3692
      },
      {
        id: 3,
        name: '清炒时蔬',
        category: '素菜',
        image: 'https://picsum.photos/60/60?random=3',
        orderCount: 128,
        revenue: 2048
      },
      {
        id: 4,
        name: '紫菜蛋花汤',
        category: '汤品',
        image: 'https://picsum.photos/60/60?random=4',
        orderCount: 98,
        revenue: 1470
      },
      {
        id: 5,
        name: '米饭',
        category: '主食',
        image: 'https://picsum.photos/60/60?random=5',
        orderCount: 89,
        revenue: 267
      }
    ]
  } catch (error) {
    console.error('加载热门菜品失败:', error)
  }
}

const initOrderChart = () => {
  if (!orderChartRef.value) return

  orderChart = echarts.init(orderChartRef.value)
  updateOrderChart()
}

const updateOrderChart = () => {
  if (!orderChart) return

  const days = orderPeriod.value === '7d' ? 7 : orderPeriod.value === '30d' ? 30 : 90
  const dates = []
  const orders = []

  for (let i = days - 1; i >= 0; i--) {
    dates.push(dayjs().subtract(i, 'day').format('MM-DD'))
    orders.push(Math.floor(Math.random() * 50) + 10)
  }

  const option = {
    tooltip: {
      trigger: 'axis',
      axisPointer: {
        type: 'cross'
      }
    },
    grid: {
      left: '3%',
      right: '4%',
      bottom: '3%',
      containLabel: true
    },
    xAxis: {
      type: 'category',
      data: dates,
      axisLine: {
        lineStyle: {
          color: '#E4E7ED'
        }
      }
    },
    yAxis: {
      type: 'value',
      axisLine: {
        lineStyle: {
          color: '#E4E7ED'
        }
      }
    },
    series: [
      {
        name: '订单数',
        type: 'line',
        smooth: true,
        symbol: 'circle',
        symbolSize: 6,
        lineStyle: {
          color: '#409EFF',
          width: 3
        },
        itemStyle: {
          color: '#409EFF'
        },
        areaStyle: {
          color: {
            type: 'linear',
            x: 0,
            y: 0,
            x2: 0,
            y2: 1,
            colorStops: [
              { offset: 0, color: 'rgba(64, 158, 255, 0.3)' },
              { offset: 1, color: 'rgba(64, 158, 255, 0.1)' }
            ]
          }
        },
        data: orders
      }
    ]
  }

  orderChart.setOption(option)
}

const initCategoryChart = () => {
  if (!categoryChartRef.value) return

  categoryChart = echarts.init(categoryChartRef.value)

  const data = [
    { value: 35, name: '热菜' },
    { value: 25, name: '凉菜' },
    { value: 20, name: '汤品' },
    { value: 15, name: '主食' },
    { value: 5, name: '甜品' }
  ]

  const option = {
    tooltip: {
      trigger: 'item',
      formatter: '{a} <br/>{b}: {c} ({d}%)'
    },
    legend: {
      orient: 'vertical',
      left: 'left'
    },
    series: [
      {
        name: '菜品分类',
        type: 'pie',
        radius: ['40%', '70%'],
        avoidLabelOverlap: false,
        label: {
          show: false,
          position: 'center'
        },
        emphasis: {
          label: {
            show: true,
            fontSize: '18',
            fontWeight: 'bold'
          }
        },
        labelLine: {
          show: false
        },
        data: data
      }
    ]
  }

  categoryChart.setOption(option)
}

const initActivityChart = () => {
  if (!activityChartRef.value) return

  activityChart = echarts.init(activityChartRef.value)

  const hours = []
  const activity = []

  for (let i = 0; i < 24; i++) {
    hours.push(i + ':00')
    activity.push(Math.floor(Math.random() * 100) + 20)
  }

  const option = {
    tooltip: {
      trigger: 'axis'
    },
    grid: {
      left: '3%',
      right: '4%',
      bottom: '3%',
      containLabel: true
    },
    xAxis: {
      type: 'category',
      data: hours,
      axisLine: {
        lineStyle: {
          color: '#E4E7ED'
        }
      }
    },
    yAxis: {
      type: 'value',
      axisLine: {
        lineStyle: {
          color: '#E4E7ED'
        }
      }
    },
    series: [
      {
        name: '活跃用户',
        type: 'bar',
        data: activity,
        itemStyle: {
          color: {
            type: 'linear',
            x: 0,
            y: 0,
            x2: 0,
            y2: 1,
            colorStops: [
              { offset: 0, color: '#67C23A' },
              { offset: 1, color: '#85CE61' }
            ]
          }
        }
      }
    ]
  }

  activityChart.setOption(option)
}

const refreshHotDishes = () => {
  loadHotDishes()
  ElMessage.success('热门菜品数据已刷新')
}

const refreshUserActivity = () => {
  initActivityChart()
  ElMessage.success('用户活跃度数据已刷新')
}

const updateRealtimeData = () => {
  realtimeData[0].value = Math.floor(Math.random() * 50) + 20
  realtimeData[1].value = Math.floor(Math.random() * 100) + 50
  realtimeData[2].value = Math.floor(Math.random() * 10) + 2
  realtimeData[3].value = Math.floor(Math.random() * 1000) + 500

  lastUpdateTime.value = dayjs().format('HH:mm:ss')
}

const startRealtimeUpdate = () => {
  realtimeTimer = setInterval(updateRealtimeData, 5000)
}

const stopRealtimeUpdate = () => {
  if (realtimeTimer) {
    clearInterval(realtimeTimer)
    realtimeTimer = null
  }
}

// 窗口大小变化时重新渲染图表
const handleResize = () => {
  if (orderChart) orderChart.resize()
  if (categoryChart) categoryChart.resize()
  if (activityChart) activityChart.resize()
}

onMounted(async () => {
  await loadCoreStats()
  await loadHotDishes()

  nextTick(() => {
    initOrderChart()
    initCategoryChart()
    initActivityChart()
  })

  startRealtimeUpdate()
  window.addEventListener('resize', handleResize)
})

onUnmounted(() => {
  stopRealtimeUpdate()
  window.removeEventListener('resize', handleResize)

  if (orderChart) {
    orderChart.dispose()
    orderChart = null
  }
  if (categoryChart) {
    categoryChart.dispose()
    categoryChart = null
  }
  if (activityChart) {
    activityChart.dispose()
    activityChart = null
  }
})
</script>

<style scoped lang="scss">
.analytics-overview {
  padding: 24px;
  background: #f7f8fa;
  min-height: 100vh;
}

.stats-row,
.charts-row,
.data-row {
  margin-bottom: 24px;
}

.chart-card,
.data-card,
.realtime-card {
  background: #fff;
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0,0,0,0.04);
  padding: 24px;
  height: 100%;
}

.chart-header,
.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
}
.chart-header h3,
.card-header h3 {
  font-size: 18px;
  font-weight: 600;
  color: #222;
}

.chart-container {
  height: 320px;
}

.hot-dishes-list {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.dish-item {
  display: flex;
  align-items: center;
  gap: 16px;
  padding: 12px;
  background: #f7f8fa;
  border-radius: 8px;
}
.dish-rank {
  width: 32px;
  height: 32px;
  background: #409EFF;
  color: #fff;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 14px;
  font-weight: bold;
}
.dish-image {
  width: 48px;
  height: 48px;
  border-radius: 8px;
  overflow: hidden;
}
.dish-info {
  flex: 1;
}
.dish-name {
  font-size: 15px;
  font-weight: 500;
  color: #222;
  margin-bottom: 2px;
}
.dish-category {
  font-size: 12px;
  color: #888;
}
.dish-stats {
  text-align: right;
}
.order-count {
  font-size: 14px;
  font-weight: 600;
  color: #409EFF;
  margin-bottom: 2px;
}
.revenue {
  font-size: 12px;
  color: #888;
}
.realtime-card .card-header .realtime-status {
  display: flex;
  align-items: center;
  gap: 12px;
}
.realtime-card .card-header .update-time {
  font-size: 13px;
  color: #888;
}
.realtime-stats {
  margin-top: 16px;
}
.realtime-item {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 16px;
  background: #f7f8fa;
  border-radius: 8px;
}
.item-icon {
  width: 48px;
  height: 48px;
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #fff;
  font-size: 22px;
}
.item-icon--primary { background: #409EFF; }
.item-icon--success { background: #67C23A; }
.item-icon--warning { background: #E6A23C; }
.item-icon--info { background: #909399; }
.item-value {
  font-size: 20px;
  font-weight: bold;
  color: #222;
}
.item-label {
  font-size: 13px;
  color: #666;
}
</style>
