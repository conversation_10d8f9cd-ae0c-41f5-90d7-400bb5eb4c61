@tailwind base;
@tailwind components;
@tailwind utilities;

/* 
@layer base {
  @font-face {
    font-family: "PingFang SC";
    font-weight: 400;
    src: url("@/assets/fonts/pingfangsc-regular.otf") format("truetype");
  }
  @font-face {
    font-family: "Alibaba";
    font-weight: 400;
    src: url("@/assets/fonts/AlibabaPuHuiTi.ttf") format("truetype");
  }
  html {
    font-family: "PingFang SC";
    moz-user-select: -moz-none;
    -moz-user-select: none;
    -o-user-select: none;
    -khtml-user-select: none;
    -webkit-user-select: none;
    -ms-user-select: none;
    user-select: none;
  }
  span,
  p,
  text {
    font-family: "PingFang SC";
    font-weight: 400;
  }
} */

@layer components {
  .flex-c {
    @apply flex justify-center items-center;
  }

  .flex-ac {
    @apply flex justify-around items-center;
  }

  .flex-bc {
    @apply flex justify-between items-center;
  }

  .navbar-bg-hover {
    @apply dark:text-white dark:hover:!bg-[#242424];
  }
}
