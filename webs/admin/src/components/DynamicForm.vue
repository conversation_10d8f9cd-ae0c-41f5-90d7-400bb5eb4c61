<template>
  <el-form
    ref="formRef"
    :model="formData"
    :rules="formRules"
    :label-width="config.labelWidth || '100px'"
    :class="config.className"
    class="dynamic-form animate__animated animate__fadeIn"
  >
    <el-row :gutter="config.gutter || 20">
      <el-col
        v-for="field in config.fields"
        :key="field.prop"
        :span="field.span || 24"
        :xs="field.xs || 24"
        :sm="field.sm || 12"
        :md="field.md || 8"
        :lg="field.lg || 6"
      >
        <el-form-item
          :label="field.label"
          :prop="field.prop"
          :required="field.required"
        >
          <!-- 输入框 -->
          <el-input
            v-if="field.type === 'input'"
            v-model="formData[field.prop]"
            :placeholder="field.placeholder"
            :disabled="field.disabled"
            :maxlength="field.maxlength"
            :show-word-limit="field.showWordLimit"
            :clearable="field.clearable !== false"
            :prefix-icon="field.prefixIcon"
            :suffix-icon="field.suffixIcon"
          />

          <!-- 密码输入框 -->
          <el-input
            v-else-if="field.type === 'password'"
            v-model="formData[field.prop]"
            type="password"
            :placeholder="field.placeholder"
            :disabled="field.disabled"
            :show-password="field.showPassword !== false"
            :clearable="field.clearable !== false"
          />

          <!-- 文本域 -->
          <el-input
            v-else-if="field.type === 'textarea'"
            v-model="formData[field.prop]"
            type="textarea"
            :placeholder="field.placeholder"
            :disabled="field.disabled"
            :rows="field.rows || 3"
            :maxlength="field.maxlength"
            :show-word-limit="field.showWordLimit"
          />

          <!-- 数字输入框 -->
          <el-input-number
            v-else-if="field.type === 'number'"
            v-model="formData[field.prop]"
            :placeholder="field.placeholder"
            :disabled="field.disabled"
            :min="field.min"
            :max="field.max"
            :step="field.step"
            :precision="field.precision"
            :controls-position="field.controlsPosition"
            style="width: 100%"
          />

          <!-- 选择器 -->
          <el-select
            v-else-if="field.type === 'select'"
            v-model="formData[field.prop]"
            :placeholder="field.placeholder"
            :disabled="field.disabled"
            :multiple="field.multiple"
            :clearable="field.clearable !== false"
            :filterable="field.filterable"
            style="width: 100%"
          >
            <el-option
              v-for="option in field.options"
              :key="option.value"
              :label="option.label"
              :value="option.value"
              :disabled="option.disabled"
            />
          </el-select>

          <!-- 级联选择器 -->
          <el-cascader
            v-else-if="field.type === 'cascader'"
            v-model="formData[field.prop]"
            :options="field.options"
            :placeholder="field.placeholder"
            :disabled="field.disabled"
            :clearable="field.clearable !== false"
            :filterable="field.filterable"
            :props="field.props"
            style="width: 100%"
          />

          <!-- 日期选择器 -->
          <el-date-picker
            v-else-if="field.type === 'date'"
            v-model="formData[field.prop]"
            type="date"
            :placeholder="field.placeholder"
            :disabled="field.disabled"
            :clearable="field.clearable !== false"
            :format="field.format"
            :value-format="field.valueFormat"
            style="width: 100%"
          />

          <!-- 日期时间选择器 -->
          <el-date-picker
            v-else-if="field.type === 'datetime'"
            v-model="formData[field.prop]"
            type="datetime"
            :placeholder="field.placeholder"
            :disabled="field.disabled"
            :clearable="field.clearable !== false"
            :format="field.format"
            :value-format="field.valueFormat"
            style="width: 100%"
          />

          <!-- 日期范围选择器 -->
          <el-date-picker
            v-else-if="field.type === 'daterange'"
            v-model="formData[field.prop]"
            type="daterange"
            :start-placeholder="field.startPlaceholder || '开始日期'"
            :end-placeholder="field.endPlaceholder || '结束日期'"
            :disabled="field.disabled"
            :clearable="field.clearable !== false"
            :format="field.format"
            :value-format="field.valueFormat"
            style="width: 100%"
          />

          <!-- 时间选择器 -->
          <el-time-picker
            v-else-if="field.type === 'time'"
            v-model="formData[field.prop]"
            :placeholder="field.placeholder"
            :disabled="field.disabled"
            :clearable="field.clearable !== false"
            style="width: 100%"
          />

          <!-- 开关 -->
          <el-switch
            v-else-if="field.type === 'switch'"
            v-model="formData[field.prop]"
            :disabled="field.disabled"
            :active-text="field.activeText"
            :inactive-text="field.inactiveText"
            :active-value="field.activeValue"
            :inactive-value="field.inactiveValue"
          />

          <!-- 单选框组 -->
          <el-radio-group
            v-else-if="field.type === 'radio'"
            v-model="formData[field.prop]"
            :disabled="field.disabled"
          >
            <el-radio
              v-for="option in field.options"
              :key="option.value"
              :label="option.value"
              :disabled="option.disabled"
            >
              {{ option.label }}
            </el-radio>
          </el-radio-group>

          <!-- 复选框组 -->
          <el-checkbox-group
            v-else-if="field.type === 'checkbox'"
            v-model="formData[field.prop]"
            :disabled="field.disabled"
          >
            <el-checkbox
              v-for="option in field.options"
              :key="option.value"
              :label="option.value"
              :disabled="option.disabled"
            >
              {{ option.label }}
            </el-checkbox>
          </el-checkbox-group>

          <!-- 滑块 -->
          <el-slider
            v-else-if="field.type === 'slider'"
            v-model="formData[field.prop]"
            :disabled="field.disabled"
            :min="field.min"
            :max="field.max"
            :step="field.step"
            :show-input="field.showInput"
            :range="field.range"
          />

          <!-- 评分 -->
          <el-rate
            v-else-if="field.type === 'rate'"
            v-model="formData[field.prop]"
            :disabled="field.disabled"
            :max="field.max"
            :allow-half="field.allowHalf"
            :show-text="field.showText"
            :texts="field.texts"
          />

          <!-- 颜色选择器 -->
          <el-color-picker
            v-else-if="field.type === 'color'"
            v-model="formData[field.prop]"
            :disabled="field.disabled"
            :show-alpha="field.showAlpha"
            :color-format="field.colorFormat"
          />

          <!-- 文件上传 -->
          <el-upload
            v-else-if="field.type === 'upload'"
            :action="field.action"
            :headers="field.headers"
            :data="field.data"
            :name="field.name"
            :multiple="field.multiple"
            :accept="field.accept"
            :list-type="field.listType"
            :auto-upload="field.autoUpload !== false"
            :disabled="field.disabled"
            :limit="field.limit"
            :on-success="(response, file, fileList) => handleUploadSuccess(field, response, file, fileList)"
            :on-error="(error, file, fileList) => handleUploadError(field, error, file, fileList)"
            :before-upload="(file) => handleBeforeUpload(field, file)"
          >
            <el-button v-if="field.listType !== 'picture-card'" type="primary">
              <el-icon><Upload /></el-icon>
              {{ field.uploadText || '点击上传' }}
            </el-button>
            <el-icon v-else><Plus /></el-icon>
          </el-upload>

          <!-- 自定义插槽 -->
          <slot
            v-else-if="field.type === 'slot'"
            :name="field.slotName"
            :field="field"
            :value="formData[field.prop]"
            :form-data="formData"
          />
        </el-form-item>
      </el-col>
    </el-row>

    <!-- 表单操作按钮 -->
    <el-form-item v-if="config.showButtons !== false" class="form-buttons">
      <el-button @click="handleReset">
        {{ config.resetText || '重置' }}
      </el-button>
      <el-button type="primary" @click="handleSubmit" :loading="loading">
        {{ config.submitText || '提交' }}
      </el-button>
      <slot name="buttons" :form-data="formData" :loading="loading" />
    </el-form-item>
  </el-form>
</template>

<script setup>
import { ref, reactive, computed, watch } from 'vue'
import { ElMessage } from 'element-plus'
import { Upload, Plus } from '@element-plus/icons-vue'

const props = defineProps({
  config: {
    type: Object,
    required: true
  },
  modelValue: {
    type: Object,
    default: () => ({})
  },
  loading: {
    type: Boolean,
    default: false
  }
})

const emit = defineEmits(['update:modelValue', 'submit', 'reset', 'upload-success', 'upload-error'])

const formRef = ref()
const formData = reactive({ ...props.modelValue })

// 表单验证规则
const formRules = computed(() => {
  const rules = {}
  props.config.fields?.forEach(field => {
    if (field.rules) {
      rules[field.prop] = field.rules
    } else if (field.required) {
      rules[field.prop] = [
        { required: true, message: `请输入${field.label}`, trigger: 'blur' }
      ]
    }
  })
  return rules
})

// 监听表单数据变化
watch(formData, (newValue) => {
  emit('update:modelValue', newValue)
}, { deep: true })

// 监听外部数据变化
watch(() => props.modelValue, (newValue) => {
  Object.assign(formData, newValue)
}, { deep: true })

// 提交表单
const handleSubmit = async () => {
  if (!formRef.value) return

  try {
    await formRef.value.validate()
    emit('submit', { ...formData })
  } catch (error) {
    console.error('表单验证失败:', error)
    ElMessage.error('请检查表单输入')
  }
}

// 重置表单
const handleReset = () => {
  if (formRef.value) {
    formRef.value.resetFields()
  }
  emit('reset')
}

// 文件上传成功
const handleUploadSuccess = (field, response, file, fileList) => {
  emit('upload-success', { field, response, file, fileList })
}

// 文件上传失败
const handleUploadError = (field, error, file, fileList) => {
  emit('upload-error', { field, error, file, fileList })
}

// 文件上传前
const handleBeforeUpload = (field, file) => {
  if (field.beforeUpload) {
    return field.beforeUpload(file)
  }
  return true
}

// 暴露方法
defineExpose({
  validate: () => formRef.value?.validate(),
  resetFields: () => formRef.value?.resetFields(),
  clearValidate: () => formRef.value?.clearValidate()
})
</script>

<style scoped lang="scss">
.dynamic-form {
  .form-buttons {
    @apply text-center mt-6;
  }

  .el-form-item {
    @apply mb-4;
  }

  .el-input,
  .el-select,
  .el-date-picker,
  .el-time-picker,
  .el-cascader {
    width: 100%;
  }
}
</style>
