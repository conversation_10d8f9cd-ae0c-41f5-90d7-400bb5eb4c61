<template>
  <div :class="cardClasses" class="stats-card">
    <div class="stats-icon">
      <el-icon :size="iconSize">
        <component :is="icon" />
      </el-icon>
    </div>
    
    <div class="stats-content">
      <div class="stats-title">{{ title }}</div>
      <div class="stats-value">{{ formattedValue }}</div>
      <div class="stats-desc" v-if="description">{{ description }}</div>
      
      <!-- 趋势指示器 -->
      <div class="stats-trend" v-if="trend">
        <el-icon :class="trendIconClass">
          <component :is="trendIcon" />
        </el-icon>
        <span :class="trendTextClass">{{ trend.value }}{{ trend.unit }}</span>
        <span class="trend-label">{{ trend.label }}</span>
      </div>
    </div>
  </div>
</template>

<script setup>
import { computed } from 'vue'
import { 
  TrendCharts, 
  ArrowUp, 
  ArrowDown,
  User,
  ShoppingCart,
  Money,
  PieChart
} from '@element-plus/icons-vue'

const props = defineProps({
  // 标题
  title: {
    type: String,
    required: true
  },
  // 数值
  value: {
    type: [Number, String],
    required: true
  },
  // 描述
  description: {
    type: String,
    default: ''
  },
  // 图标
  icon: {
    type: [String, Object],
    default: () => PieChart
  },
  // 图标大小
  iconSize: {
    type: Number,
    default: 24
  },
  // 卡片类型/颜色主题
  type: {
    type: String,
    default: 'primary',
    validator: (value) => ['primary', 'success', 'warning', 'danger', 'info'].includes(value)
  },
  // 趋势数据
  trend: {
    type: Object,
    default: null
    // 格式: { value: 12, unit: '%', label: '较上月', type: 'up' | 'down' }
  },
  // 数值格式化
  formatter: {
    type: Function,
    default: null
  },
  // 是否显示动画
  animated: {
    type: Boolean,
    default: true
  }
})

// 格式化数值
const formattedValue = computed(() => {
  if (props.formatter) {
    return props.formatter(props.value)
  }
  
  if (typeof props.value === 'number') {
    // 自动格式化大数字
    if (props.value >= 10000) {
      return (props.value / 10000).toFixed(1) + '万'
    } else if (props.value >= 1000) {
      return (props.value / 1000).toFixed(1) + 'k'
    }
  }
  
  return props.value
})

// 卡片样式类
const cardClasses = computed(() => {
  const baseClasses = 'stats-card'
  const typeClasses = {
    primary: 'stats-card--primary',
    success: 'stats-card--success',
    warning: 'stats-card--warning',
    danger: 'stats-card--danger',
    info: 'stats-card--info'
  }
  
  return [
    baseClasses,
    typeClasses[props.type],
    {
      'stats-card--animated': props.animated
    }
  ]
})

// 趋势图标
const trendIcon = computed(() => {
  if (!props.trend) return null
  return props.trend.type === 'up' ? ArrowUp : ArrowDown
})

// 趋势图标样式
const trendIconClass = computed(() => {
  if (!props.trend) return ''
  return props.trend.type === 'up' ? 'trend-up' : 'trend-down'
})

// 趋势文本样式
const trendTextClass = computed(() => {
  if (!props.trend) return ''
  return props.trend.type === 'up' ? 'trend-up' : 'trend-down'
})
</script>

<style scoped lang="scss">
.stats-card {
  background: white;
  border-radius: 12px;
  padding: 24px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  display: flex;
  align-items: center;
  gap: 16px;
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
  
  &:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 16px rgba(0, 0, 0, 0.15);
  }
  
  &--animated {
    .stats-value {
      animation: countUp 1s ease-out;
    }
  }
  
  // 渐变背景
  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: linear-gradient(90deg, var(--card-color-start), var(--card-color-end));
  }
  
  &--primary {
    --card-color-start: #667eea;
    --card-color-end: #764ba2;
    
    .stats-icon {
      background: linear-gradient(135deg, #667eea, #764ba2);
    }
  }
  
  &--success {
    --card-color-start: #4facfe;
    --card-color-end: #00f2fe;
    
    .stats-icon {
      background: linear-gradient(135deg, #4facfe, #00f2fe);
    }
  }
  
  &--warning {
    --card-color-start: #fa709a;
    --card-color-end: #fee140;
    
    .stats-icon {
      background: linear-gradient(135deg, #fa709a, #fee140);
    }
  }
  
  &--danger {
    --card-color-start: #ff9a9e;
    --card-color-end: #fecfef;
    
    .stats-icon {
      background: linear-gradient(135deg, #ff9a9e, #fecfef);
    }
  }
  
  &--info {
    --card-color-start: #a8edea;
    --card-color-end: #fed6e3;
    
    .stats-icon {
      background: linear-gradient(135deg, #a8edea, #fed6e3);
    }
  }
}

.stats-icon {
  width: 60px;
  height: 60px;
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  flex-shrink: 0;
}

.stats-content {
  flex: 1;
}

.stats-title {
  font-size: 14px;
  color: #666;
  margin-bottom: 8px;
  font-weight: 500;
}

.stats-value {
  font-size: 32px;
  font-weight: bold;
  color: #333;
  margin-bottom: 4px;
  line-height: 1;
}

.stats-desc {
  font-size: 12px;
  color: #999;
  margin-bottom: 8px;
}

.stats-trend {
  display: flex;
  align-items: center;
  gap: 4px;
  font-size: 12px;
  
  .trend-up {
    color: #52c41a;
  }
  
  .trend-down {
    color: #ff4d4f;
  }
  
  .trend-label {
    color: #999;
    margin-left: 4px;
  }
}

@keyframes countUp {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

// 响应式设计
@media (max-width: 768px) {
  .stats-card {
    padding: 16px;
    gap: 12px;
  }
  
  .stats-icon {
    width: 48px;
    height: 48px;
  }
  
  .stats-value {
    font-size: 24px;
  }
}
</style>
