import {defineStore} from 'pinia';
import {ref, computed} from 'vue';
import {userApi} from '@/api/user';

export const useUserStore = defineStore('user', () => {
  // 状态
  const token = ref(localStorage.getItem('admin_token') || '');
  const userInfo = ref(null);

  // 计算属性
  const isLoggedIn = computed(() => !!token.value);
  const userName = computed(() => userInfo.value?.name || '');
  const userRole = computed(() => userInfo.value?.role || 'user');

  // 登录
  const login = async credentials => {
    try {
      // 调用真实的登录API
      const response = await fetch('/api/auth/login', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          username: credentials.username,
          password: credentials.password,
          loginType: 'password'
        })
      });

      const result = await response.json();

      if (result.code === 200 && result.data) {
        token.value = result.data.token;
        userInfo.value = result.data.user;

        // 保存到本地存储
        localStorage.setItem('admin_token', token.value);
        localStorage.setItem('admin_user', JSON.stringify(userInfo.value));

        return {success: true};
      } else {
        return {success: false, message: result.message || '登录失败'};
      }
    } catch (error) {
      console.error('登录失败:', error);
      return {success: false, message: '登录失败，请检查网络连接'};
    }
  };

  // 退出登录
  const logout = () => {
    token.value = '';
    userInfo.value = null;

    // 清除本地存储
    localStorage.removeItem('admin_token');
    localStorage.removeItem('admin_user');
  };

  // 初始化用户信息
  const initUserInfo = () => {
    const userStr = localStorage.getItem('admin_user');
    if (userStr) {
      try {
        userInfo.value = JSON.parse(userStr);
      } catch (error) {
        console.error('解析用户信息失败:', error);
        logout();
      }
    }
  };

  // 初始化
  initUserInfo();

  return {
    // 状态
    token,
    userInfo,

    // 计算属性
    isLoggedIn,
    userName,
    userRole,

    // 方法
    login,
    logout,
    initUserInfo
  };
});
