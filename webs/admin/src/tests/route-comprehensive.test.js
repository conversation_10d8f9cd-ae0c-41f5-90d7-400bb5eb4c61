// 路由功能完整性测试 - 使用真实API
import {describe, it, expect, beforeAll, afterAll, vi} from 'vitest';
import {mount} from '@vue/test-utils';
import {createRouter, createWebHistory} from 'vue-router';
import {createPinia} from 'pinia';
import axios from 'axios';

// 导入路由配置
import router from '@/router/index.js';

// 配置axios基础URL
axios.defaults.baseURL = 'http://localhost:3000';

// 测试用户凭据
const TEST_USER = {
  username: '13800138000',
  password: '123456'
};

let authToken = null;

// 检查服务器是否运行
async function checkServerStatus() {
  try {
    const response = await axios.get('/', {timeout: 5000});
    return true;
  } catch (error) {
    if (error.code === 'ECONNREFUSED') {
      console.error(
        '❌ 服务器未启动，请先启动后端服务器 (npm run dev 在 webs/server 目录)'
      );
      return false;
    }
    return true;
  }
}

// 获取认证token
async function getAuthToken() {
  try {
    const response = await axios.post('/api/auth/login', {
      username: TEST_USER.username,
      password: TEST_USER.password,
      loginType: 'password'
    });

    if (response.data.code === 200) {
      return response.data.data.token;
    } else {
      throw new Error(`登录失败: ${response.data.message}`);
    }
  } catch (error) {
    throw new Error(`无法获取认证token: ${error.message}`);
  }
}

// 测试API端点
async function testApiEndpoint(endpoint, method = 'GET', data = null) {
  try {
    const config = {
      method,
      url: endpoint,
      headers: {
        Authorization: `Bearer ${authToken}`,
        'Content-Type': 'application/json'
      }
    };

    if (data && method !== 'GET') {
      config.data = data;
    }

    const response = await axios(config);
    return {
      success: true,
      status: response.status,
      data: response.data
    };
  } catch (error) {
    return {
      success: false,
      status: error.response?.status || 0,
      error: error.message
    };
  }
}

describe('路由功能完整性测试', () => {
  let testRouter;
  let pinia;

  beforeAll(async () => {
    // 检查服务器状态
    const serverRunning = await checkServerStatus();
    if (!serverRunning) {
      throw new Error('后端服务器未启动，请先启动服务器');
    }

    // 获取认证token
    try {
      authToken = await getAuthToken();
      console.log('✅ 成功获取认证token');
    } catch (error) {
      console.error('❌ 获取认证token失败:', error.message);
      throw error;
    }

    // 设置axios默认headers
    axios.defaults.headers.common['Authorization'] = `Bearer ${authToken}`;
  });

  beforeEach(() => {
    // 创建路由实例 - 使用内存历史记录避免浏览器API问题
    testRouter = createRouter({
      history: createWebHistory('/'),
      routes: [
        {path: '/', redirect: '/dashboard'},
        {
          path: '/dashboard',
          name: 'Dashboard',
          component: {template: '<div>Dashboard</div>'}
        },
        {
          path: '/login',
          name: 'Login',
          component: {template: '<div>Login</div>'}
        },
        {
          path: '/:pathMatch(.*)*',
          name: 'NotFound',
          component: {template: '<div>404</div>'}
        }
      ]
    });

    // 创建Pinia实例
    pinia = createPinia();

    // 设置localStorage中的token
    window.localStorage.getItem = vi.fn(() => authToken);
  });

  // 测试API端点可访问性
  describe('API端点测试', () => {
    const apiEndpoints = [
      {path: '/api/menus', name: '菜单列表'},
      {path: '/api/menus/today', name: '今日菜单'},
      {path: '/api/menus/categories', name: '菜品分类'},
      {path: '/api/menus/statistics', name: '菜单统计'},
      {path: '/api/dishes', name: '菜品列表'},
      {path: '/api/orders', name: '订单列表'},
      {path: '/api/users', name: '用户列表'},
      {path: '/api/messages', name: '消息列表'},
      {path: '/api/notifications', name: '通知列表'}
    ];

    apiEndpoints.forEach(endpoint => {
      it(`${endpoint.name} API (${endpoint.path}) 应该可以访问`, async () => {
        const result = await testApiEndpoint(endpoint.path);

        if (!result.success) {
          console.error(`❌ ${endpoint.name} API 访问失败:`, result.error);
        }

        expect(result.success).toBe(true);
        expect(result.status).toBe(200);
      });
    });
  });

  // 测试路由可访问性
  describe('路由可访问性测试', () => {
    const testRoutes = [
      {path: '/dashboard', name: '仪表盘'},
      {path: '/login', name: '登录页面'}
    ];

    testRoutes.forEach(route => {
      it(`应该能够访问 ${route.name} (${route.path})`, async () => {
        await testRouter.push(route.path);
        expect(testRouter.currentRoute.value.path).toBe(route.path);
      });
    });
  });

  // 测试路由守卫
  describe('路由守卫测试', () => {
    it('未登录用户访问受保护路由应该重定向到登录页', async () => {
      // 清除token
      window.localStorage.getItem = vi.fn(() => null);

      await testRouter.push('/dashboard');

      // 由于路由守卫，应该重定向到登录页
      expect(testRouter.currentRoute.value.path).toBe('/login');
    });

    it('已登录用户应该能够访问受保护路由', async () => {
      // 设置token
      window.localStorage.getItem = vi.fn(() => authToken);

      await testRouter.push('/dashboard');
      expect(testRouter.currentRoute.value.path).toBe('/dashboard');
    });
  });

  // 测试404路由
  describe('404路由测试', () => {
    const invalidRoutes = ['/nonexistent', '/invalid/path', '/random/route'];

    invalidRoutes.forEach(route => {
      it(`访问无效路由 ${route} 应该显示404页面`, async () => {
        await testRouter.push(route);
        expect(testRouter.currentRoute.value.name).toBe('NotFound');
      });
    });
  });

  // 测试重定向
  describe('重定向测试', () => {
    it('根路径应该重定向到仪表盘', async () => {
      await testRouter.push('/');
      expect(testRouter.currentRoute.value.path).toBe('/dashboard');
    });
  });
});
