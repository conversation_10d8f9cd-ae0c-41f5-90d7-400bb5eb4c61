const map = new WeakMap();
// 创建一个 WeakMap 对象，用于存储元素和对应的回调函数的映射关系，使用 WeakMap 的原因是它可以避免内存泄漏

const ob = new ResizeObserver(entries => {
  for (const entry of entries) {
    // 遍历 ResizeObserver 的回调函数中传入的所有变化的元素的 entries 数组
    const handler = map.get(entry.target);
    // 获取该元素对应的回调函数

    if (handler) {
      const box = entry.borderBoxSize[0];
      // 获取元素大小的边框盒子对象

      handler({
        width: box.inlineSize,
        height: box.blockSize
      });
      // 调用回调函数，并传入元素的新尺寸作为参数
    }
  }
});

export default {
  mounted(el, binding) {
    // 在 Vue 组件的 mounted 钩子中调用
    // 监视指定的元素 el 的尺寸变化

    ob.observe(el);
    // 将元素 el 添加到 ResizeObserver 中进行观察

    map.set(el, binding.value);
    // 将元素 el 和对应的回调函数 binding.value 存储到 WeakMap 中
  }
};
