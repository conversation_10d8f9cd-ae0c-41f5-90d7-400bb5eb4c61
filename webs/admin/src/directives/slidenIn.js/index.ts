// 列表进入时向上移动效果的指令
export default {
  mounted(el) {
    const DISTANCE = 150;
    const DURATION = 2;
    // eslint-disable-next-line @typescript-eslint/no-unused-vars, no-unused-vars
    const animation = el.animate(
      [
        {
          transform: `translateY(${DISTANCE})`,
          opacity: 0.5
        },
        {
          transform: `translateY(0)`,
          opacity: 1
        }
      ],
      {
        //跟css对应
        duration: DURATION,
        easing: "ease-in-out",

        fill: "forwards"
      }
    );
  }
};
