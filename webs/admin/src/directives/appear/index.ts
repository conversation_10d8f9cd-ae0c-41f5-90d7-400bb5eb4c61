import { useMotion } from "@vueuse/motion";
import { useIntersectionObserver, useElementVisibility } from "@vueuse/core";
/**
 * @description 滚动动画
 * @param {Object} options
 * @param {Object} options.initial
 * @param {Object} options.enter
 * @param {Number} options.delay
 * <AUTHOR>
 */
export default {
  mounted(el, binding) {
    const options = binding.value || {};
    // console.log("111", options);

    const targetIsVisible = useElementVisibility(el);

    if (targetIsVisible.value) {
      useMotion(el, {
        initial: options.initial || { opacity: 0, y: 100 },
        enter: options.enter || {
          opacity: 1,
          y: 0,
          transition: { delay: options?.delay || 0 }
        }
      });
      return;
    }

    const motionInstance = useMotion(el, {
      initial: options.initial || { opacity: 0, y: 100 }
    });

    const { stop } = useIntersectionObserver(el, ([{ isIntersecting }]) => {
      if (isIntersecting) {
        motionInstance.apply({
          opacity: 1,
          y: 0,
          transition: { delay: options?.delay || 0 }
        });
        stop();
      }
    });
  }
};
