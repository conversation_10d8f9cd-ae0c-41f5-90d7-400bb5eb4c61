#!/usr/bin/env node

/**
 * 用户管理系统完整功能测试
 * 测试用户的增删改查、权限管理、批量操作等功能
 */

const axios = require('axios');

// 配置
const BASE_URL = 'http://localhost:3000/api';
const ADMIN_TOKEN = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ1c2VySWQiOiJjbTRhZGZhZGYwMDAwMTJkZjAwMDAwMDAiLCJyb2xlIjoiYWRtaW4iLCJpYXQiOjE3MzI4NzI0MjQsImV4cCI6MTczMzQ3NzIyNH0.Hs8Hs8Hs8Hs8Hs8Hs8Hs8Hs8Hs8Hs8Hs8Hs8Hs8Hs8'; // 需要替换为实际的管理员token

// 创建axios实例
const api = axios.create({
  baseURL: BASE_URL,
  headers: {
    'Authorization': `Bearer ${ADMIN_TOKEN}`,
    'Content-Type': 'application/json'
  }
});

// 测试数据
const testUsers = [
  {
    name: '测试用户1',
    phone: '13800000001',
    password: '123456',
    role: 'user'
  },
  {
    name: '测试用户2',
    phone: '13800000002',
    password: '123456',
    role: 'family'
  },
  {
    name: '测试用户3',
    phone: '13800000003',
    password: '123456',
    role: 'user'
  }
];

// 测试结果统计
let testResults = {
  total: 0,
  passed: 0,
  failed: 0,
  errors: []
};

// 测试工具函数
function logTest(name, success, error = null) {
  testResults.total++;
  if (success) {
    testResults.passed++;
    console.log(`✅ ${name}`);
  } else {
    testResults.failed++;
    console.log(`❌ ${name}: ${error}`);
    testResults.errors.push({ name, error });
  }
}

// 延迟函数
const delay = (ms) => new Promise(resolve => setTimeout(resolve, ms));

// 1. 测试获取用户列表
async function testGetUsers() {
  try {
    const response = await api.get('/users');
    logTest('获取用户列表', response.status === 200 && response.data.success);
    return response.data.data;
  } catch (error) {
    logTest('获取用户列表', false, error.message);
    return null;
  }
}

// 2. 测试获取用户列表（带分页和搜索）
async function testGetUsersWithParams() {
  try {
    const response = await api.get('/users', {
      params: {
        page: 1,
        size: 10,
        search: '测试',
        role: 'user'
      }
    });
    logTest('获取用户列表（带参数）', response.status === 200 && response.data.success);
    return response.data.data;
  } catch (error) {
    logTest('获取用户列表（带参数）', false, error.message);
    return null;
  }
}

// 3. 测试创建用户
async function testCreateUser(userData) {
  try {
    const response = await api.post('/users', userData);
    logTest(`创建用户 ${userData.name}`, response.status === 200 && response.data.success);
    return response.data.data;
  } catch (error) {
    logTest(`创建用户 ${userData.name}`, false, error.message);
    return null;
  }
}

// 4. 测试获取用户详情
async function testGetUserDetail(userId) {
  try {
    const response = await api.get(`/users/${userId}`);
    logTest('获取用户详情', response.status === 200 && response.data.success);
    return response.data.data;
  } catch (error) {
    logTest('获取用户详情', false, error.message);
    return null;
  }
}

// 5. 测试更新用户
async function testUpdateUser(userId, updateData) {
  try {
    const response = await api.put(`/users/${userId}`, updateData);
    logTest('更新用户信息', response.status === 200 && response.data.success);
    return response.data.data;
  } catch (error) {
    logTest('更新用户信息', false, error.message);
    return null;
  }
}

// 6. 测试重置密码
async function testResetPassword(userId) {
  try {
    const response = await api.put(`/users/${userId}/password`, {
      password: 'newpassword123'
    });
    logTest('重置用户密码', response.status === 200 && response.data.success);
    return true;
  } catch (error) {
    logTest('重置用户密码', false, error.message);
    return false;
  }
}

// 7. 测试获取用户统计
async function testGetUserStatistics() {
  try {
    const response = await api.get('/users/statistics');
    logTest('获取用户统计', response.status === 200 && response.data.success);
    return response.data.data;
  } catch (error) {
    logTest('获取用户统计', false, error.message);
    return null;
  }
}

// 8. 测试批量删除用户
async function testBatchDeleteUsers(userIds) {
  try {
    const response = await api.delete('/users/batch', {
      data: { ids: userIds }
    });
    logTest('批量删除用户', response.status === 200 && response.data.success);
    return true;
  } catch (error) {
    logTest('批量删除用户', false, error.message);
    return false;
  }
}

// 9. 测试删除单个用户
async function testDeleteUser(userId) {
  try {
    const response = await api.delete(`/users/${userId}`);
    logTest('删除单个用户', response.status === 200 && response.data.success);
    return true;
  } catch (error) {
    logTest('删除单个用户', false, error.message);
    return false;
  }
}

// 10. 测试获取家庭成员列表
async function testGetFamilyMembers() {
  try {
    const response = await api.get('/users/family');
    logTest('获取家庭成员列表', response.status === 200 && response.data.success);
    return response.data.data;
  } catch (error) {
    logTest('获取家庭成员列表', false, error.message);
    return null;
  }
}

// 主测试函数
async function runTests() {
  console.log('🚀 开始用户管理系统功能测试...\n');

  // 存储创建的用户ID，用于后续测试
  const createdUserIds = [];

  try {
    // 1. 测试获取用户列表
    console.log('📋 测试用户列表功能...');
    await testGetUsers();
    await testGetUsersWithParams();
    await delay(500);

    // 2. 测试创建用户
    console.log('\n👤 测试用户创建功能...');
    for (const userData of testUsers) {
      const createdUser = await testCreateUser(userData);
      if (createdUser && createdUser.id) {
        createdUserIds.push(createdUser.id);
      }
      await delay(300);
    }

    // 3. 测试用户详情和更新
    if (createdUserIds.length > 0) {
      console.log('\n🔍 测试用户详情和更新功能...');
      const userId = createdUserIds[0];
      
      await testGetUserDetail(userId);
      await testUpdateUser(userId, {
        name: '更新后的用户名',
        role: 'family'
      });
      await testResetPassword(userId);
      await delay(500);
    }

    // 4. 测试统计功能
    console.log('\n📊 测试用户统计功能...');
    await testGetUserStatistics();
    await testGetFamilyMembers();
    await delay(500);

    // 5. 测试批量删除（删除前两个用户）
    if (createdUserIds.length >= 2) {
      console.log('\n🗑️ 测试批量删除功能...');
      const batchDeleteIds = createdUserIds.slice(0, 2);
      await testBatchDeleteUsers(batchDeleteIds);
      await delay(500);
    }

    // 6. 测试单个删除（删除剩余用户）
    if (createdUserIds.length >= 3) {
      console.log('\n🗑️ 测试单个删除功能...');
      await testDeleteUser(createdUserIds[2]);
    }

  } catch (error) {
    console.error('测试过程中发生错误:', error);
  }

  // 输出测试结果
  console.log('\n' + '='.repeat(50));
  console.log('📊 测试结果统计:');
  console.log(`总测试数: ${testResults.total}`);
  console.log(`通过: ${testResults.passed} ✅`);
  console.log(`失败: ${testResults.failed} ❌`);
  console.log(`成功率: ${((testResults.passed / testResults.total) * 100).toFixed(2)}%`);

  if (testResults.errors.length > 0) {
    console.log('\n❌ 失败的测试:');
    testResults.errors.forEach(({ name, error }) => {
      console.log(`  - ${name}: ${error}`);
    });
  }

  console.log('\n🎉 用户管理系统功能测试完成!');
}

// 运行测试
if (require.main === module) {
  runTests().catch(console.error);
}

module.exports = {
  runTests,
  testResults
};
