@echo off
echo 🍽️ 启动楠楠家厨后台管理系统...
echo.

cd /d "%~dp0"

echo 📁 当前目录: %CD%
echo.

echo 🔍 检查依赖...
if not exist "node_modules" (
    echo ❌ 依赖未安装，正在安装...
    npm install
    if errorlevel 1 (
        echo ❌ 依赖安装失败！
        pause
        exit /b 1
    )
    echo ✅ 依赖安装完成！
    echo.
)

echo 🚀 启动开发服务器...
echo.
echo 📱 访问地址: http://localhost:5173
echo 🔑 测试账号: 13800138000 / 123456
echo.
echo 按 Ctrl+C 停止服务器
echo.

npx vite --port 5173 --host 0.0.0.0

pause
