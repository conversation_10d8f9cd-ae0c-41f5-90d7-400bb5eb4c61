// 简单的API测试脚本
const http = require('http');

// 测试API连接
function testAPI() {
  const options = {
    hostname: 'localhost',
    port: 3000,
    path: '/api/auth/login',
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    }
  };

  const postData = JSON.stringify({
    phone: '13800000000',
    password: '123456'
  });

  const req = http.request(options, (res) => {
    console.log(`状态码: ${res.statusCode}`);
    console.log(`响应头: ${JSON.stringify(res.headers)}`);
    
    let data = '';
    res.on('data', (chunk) => {
      data += chunk;
    });
    
    res.on('end', () => {
      console.log('响应数据:', data);
      try {
        const response = JSON.parse(data);
        if (response.success && response.data.token) {
          console.log('✅ 登录成功，获取到token');
          testUserAPI(response.data.token);
        } else {
          console.log('❌ 登录失败');
        }
      } catch (error) {
        console.log('❌ 解析响应失败:', error.message);
      }
    });
  });

  req.on('error', (e) => {
    console.error(`请求遇到问题: ${e.message}`);
  });

  req.write(postData);
  req.end();
}

// 测试用户API
function testUserAPI(token) {
  const options = {
    hostname: 'localhost',
    port: 3000,
    path: '/api/users',
    method: 'GET',
    headers: {
      'Authorization': `Bearer ${token}`,
      'Content-Type': 'application/json',
    }
  };

  const req = http.request(options, (res) => {
    console.log(`\n用户API状态码: ${res.statusCode}`);
    
    let data = '';
    res.on('data', (chunk) => {
      data += chunk;
    });
    
    res.on('end', () => {
      try {
        const response = JSON.parse(data);
        if (response.success) {
          console.log('✅ 用户列表获取成功');
          console.log(`用户数量: ${response.data.list ? response.data.list.length : 0}`);
          testDishAPI(token);
        } else {
          console.log('❌ 用户列表获取失败:', response.message);
        }
      } catch (error) {
        console.log('❌ 解析用户API响应失败:', error.message);
      }
    });
  });

  req.on('error', (e) => {
    console.error(`用户API请求遇到问题: ${e.message}`);
  });

  req.end();
}

// 测试菜品API
function testDishAPI(token) {
  const options = {
    hostname: 'localhost',
    port: 3000,
    path: '/api/dishes',
    method: 'GET',
    headers: {
      'Authorization': `Bearer ${token}`,
      'Content-Type': 'application/json',
    }
  };

  const req = http.request(options, (res) => {
    console.log(`\n菜品API状态码: ${res.statusCode}`);
    
    let data = '';
    res.on('data', (chunk) => {
      data += chunk;
    });
    
    res.on('end', () => {
      try {
        const response = JSON.parse(data);
        if (response.success) {
          console.log('✅ 菜品列表获取成功');
          console.log(`菜品数量: ${response.data.list ? response.data.list.length : 0}`);
          testCreateDish(token);
        } else {
          console.log('❌ 菜品列表获取失败:', response.message);
        }
      } catch (error) {
        console.log('❌ 解析菜品API响应失败:', error.message);
      }
    });
  });

  req.on('error', (e) => {
    console.error(`菜品API请求遇到问题: ${e.message}`);
  });

  req.end();
}

// 测试创建菜品
function testCreateDish(token) {
  const options = {
    hostname: 'localhost',
    port: 3000,
    path: '/api/dishes',
    method: 'POST',
    headers: {
      'Authorization': `Bearer ${token}`,
      'Content-Type': 'application/json',
    }
  };

  const postData = JSON.stringify({
    name: '测试菜品' + Date.now(),
    description: '这是一个测试菜品',
    category: '热菜',
    image: 'https://example.com/test.jpg'
  });

  const req = http.request(options, (res) => {
    console.log(`\n创建菜品API状态码: ${res.statusCode}`);
    
    let data = '';
    res.on('data', (chunk) => {
      data += chunk;
    });
    
    res.on('end', () => {
      try {
        const response = JSON.parse(data);
        if (response.success) {
          console.log('✅ 菜品创建成功');
          console.log(`菜品ID: ${response.data.id}`);
          console.log('\n🎉 所有基础API测试完成！');
        } else {
          console.log('❌ 菜品创建失败:', response.message);
        }
      } catch (error) {
        console.log('❌ 解析创建菜品API响应失败:', error.message);
      }
    });
  });

  req.on('error', (e) => {
    console.error(`创建菜品API请求遇到问题: ${e.message}`);
  });

  req.write(postData);
  req.end();
}

console.log('🚀 开始API功能测试...\n');
testAPI();
