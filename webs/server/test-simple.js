require('dotenv').config();
const axios = require('axios');

const BASE_URL = 'http://localhost:3000/api';

async function testSimple() {
  console.log('🧪 简单测试开始...');

  try {
    // 1. 测试API连接
    console.log('1️⃣ 测试API连接...');
    const apiRes = await axios.get('http://localhost:3000');
    console.log('✅ API连接成功:', apiRes.data.message);

    // 2. 测试用户注册
    console.log('\n2️⃣ 测试用户注册...');
    const randomPhone = `139${Math.floor(Math.random() * 100000000).toString().padStart(8, '0')}`;
    
    const userData = {
      name: '测试用户',
      phone: randomPhone,
      password: '123456'
    };

    const registerRes = await axios.post(`${BASE_URL}/auth/register`, userData);
    console.log('✅ 用户注册成功:', registerRes.data.data.user.name);
    
    const token = registerRes.data.data.token;

    // 3. 测试创建菜品
    console.log('\n3️⃣ 测试创建菜品...');
    const dishData = {
      name: '测试菜品',
      description: '这是一道测试菜品',
      category: '热菜',
      image: 'https://example.com/test.jpg'
    };

    const dishRes = await axios.post(`${BASE_URL}/dishes`, dishData, {
      headers: { Authorization: `Bearer ${token}` }
    });
    
    if (dishRes.data.code === 201) {
      console.log('✅ 菜品创建成功:', dishRes.data.data.name);
    }

    // 4. 测试获取通知
    console.log('\n4️⃣ 测试获取通知...');
    
    // 等待通知创建
    await new Promise(resolve => setTimeout(resolve, 2000));
    
    const notificationsRes = await axios.get(`${BASE_URL}/notifications`, {
      headers: { Authorization: `Bearer ${token}` }
    });
    
    if (notificationsRes.data.code === 200) {
      console.log('✅ 通知获取成功');
      console.log('   通知数量:', notificationsRes.data.data.notifications.length);
      console.log('   未读数量:', notificationsRes.data.data.unreadCount);
    }

    console.log('\n🎉 简单测试完成！');

  } catch (error) {
    console.error('❌ 测试失败:', error.response?.data || error.message);
    if (error.response) {
      console.error('状态码:', error.response.status);
      console.error('响应数据:', error.response.data);
    }
  }
}

// 运行测试
testSimple();
