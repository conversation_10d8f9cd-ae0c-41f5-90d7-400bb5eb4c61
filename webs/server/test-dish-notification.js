require('dotenv').config();
const axios = require('axios');

const BASE_URL = 'http://localhost:3000/api';

async function testDishNotification() {
  console.log('🧪 测试菜品创建通知...');

  try {
    // 1. 注册两个用户
    console.log('1️⃣ 注册两个测试用户...');

    const user1Phone = `139${Math.floor(Math.random() * 100000000)
      .toString()
      .padStart(8, '0')}`;
    const user2Phone = `138${Math.floor(Math.random() * 100000000)
      .toString()
      .padStart(8, '0')}`;

    const user1Data = {
      name: '张三',
      phone: user1Phone,
      password: '123456'
    };

    const user2Data = {
      name: '李四',
      phone: user2Phone,
      password: '123456'
    };

    const user1Res = await axios.post(`${BASE_URL}/auth/register`, user1Data);
    const user2Res = await axios.post(`${BASE_URL}/auth/register`, user2Data);

    const user1Token = user1Res.data.data.token;
    const user2Token = user2Res.data.data.token;

    console.log('✅ 用户注册成功');
    console.log(
      '   用户1:',
      user1Res.data.data.user.name,
      '- ID:',
      user1Res.data.data.user.id
    );
    console.log(
      '   用户2:',
      user2Res.data.data.user.name,
      '- ID:',
      user2Res.data.data.user.id
    );

    // 2. 用户1创建菜品
    console.log('\n2️⃣ 用户1创建菜品...');

    const dishData = {
      name: `通知测试菜品_${Date.now()}`,
      description: '这是一道用于测试通知功能的菜品',
      category: '热菜',
      image:
        'https://images.pexels.com/photos/2097090/pexels-photo-2097090.jpeg'
    };

    const dishRes = await axios.post(`${BASE_URL}/dishes`, dishData, {
      headers: {Authorization: `Bearer ${user1Token}`}
    });

    if (dishRes.data.code === 201) {
      console.log('✅ 菜品创建成功:', dishRes.data.data.name);
      console.log('   菜品ID:', dishRes.data.data.id);
    } else {
      console.log('❌ 菜品创建失败:', dishRes.data);
      return;
    }

    // 3. 等待通知处理
    console.log('\n3️⃣ 等待通知处理...');
    await new Promise(resolve => setTimeout(resolve, 3000));

    // 4. 检查用户2的通知
    console.log('\n4️⃣ 检查用户2的通知...');

    const user2NotificationsRes = await axios.get(`${BASE_URL}/notifications`, {
      headers: {Authorization: `Bearer ${user2Token}`}
    });

    if (user2NotificationsRes.data.code === 200) {
      const notifications = user2NotificationsRes.data.data.notifications;
      console.log('✅ 用户2通知获取成功');
      console.log('   通知数量:', notifications.length);
      console.log('   未读数量:', user2NotificationsRes.data.data.unreadCount);

      if (notifications.length > 0) {
        console.log('   最新通知:', notifications[0].content);
        console.log('   通知类型:', notifications[0].type);
      } else {
        console.log('⚠️ 用户2没有收到通知');
      }
    }

    // 5. 检查用户1的通知（应该没有，因为是自己创建的）
    console.log('\n5️⃣ 检查用户1的通知...');

    const user1NotificationsRes = await axios.get(`${BASE_URL}/notifications`, {
      headers: {Authorization: `Bearer ${user1Token}`}
    });

    if (user1NotificationsRes.data.code === 200) {
      const notifications = user1NotificationsRes.data.data.notifications;
      console.log('✅ 用户1通知获取成功');
      console.log('   通知数量:', notifications.length);
      console.log('   未读数量:', user1NotificationsRes.data.data.unreadCount);

      if (notifications.length > 0) {
        console.log('   最新通知:', notifications[0].content);
      }
    }

    // 6. 测试未读数量API
    console.log('\n6️⃣ 测试未读数量API...');

    const unreadCountRes = await axios.get(
      `${BASE_URL}/notifications/unread-count`,
      {
        headers: {Authorization: `Bearer ${user2Token}`}
      }
    );

    if (unreadCountRes.data.code === 200) {
      console.log('✅ 未读数量API正常:', unreadCountRes.data.data.count);
    }

    console.log('\n🎉 菜品创建通知测试完成！');
  } catch (error) {
    console.error('❌ 测试失败:', error.response?.data || error.message);
    if (error.response) {
      console.error('状态码:', error.response.status);
      console.error('响应数据:', error.response.data);
    }
  }
}

// 运行测试
testDishNotification();
