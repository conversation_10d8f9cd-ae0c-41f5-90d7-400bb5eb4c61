# 🚀 楠楠家厨数据库快速启动指南

## 📋 每日启动流程

### 1. 一键启动（推荐）
```bash
cd webs/server
npm run quick-start
```

### 2. 手动启动
```bash
cd webs/server
npm run generate    # 生成 Prisma 客户端
npm run db:push     # 同步数据库模式  
npm run dev         # 启动开发服务器
```

### 3. 启动数据库管理界面（可选）
```bash
npm run studio      # 访问 http://localhost:5555
```

## 🔄 数据导出导入

### 从 Neon Console 导出数据
```bash
npm run db:export
```
- 导出文件保存在 `exports/` 目录
- 文件名包含时间戳，如：`data-export-2025-05-28T07-26-04-455Z.json`

### 导入数据到本地
```bash
npm run db:import <文件名>
# 例如：
npm run db:import data-export-2025-05-28T07-26-04-455Z.json
```
⚠️ **注意**: 导入会清空现有数据，请先备份！

## 🛠️ 常用维护命令

| 命令                | 说明                         |
| ------------------- | ---------------------------- |
| `npm run db:test`   | 测试数据库连接和查看数据统计 |
| `npm run db:clear`  | 清空所有数据                 |
| `npm run db:seed`   | 创建测试数据                 |
| `npm run db:export` | 导出当前数据                 |

## 🌐 访问地址

- **后端 API**: http://localhost:3000
- **Prisma Studio**: http://localhost:5555
- **API 文档**: http://localhost:3000 (根路径)

## 📁 重要文件说明

```
webs/server/
├── .env                    # 环境变量配置
├── prisma/schema.prisma    # 数据库模式定义
├── exports/               # 数据导出文件目录
├── scripts/               # 数据库操作脚本
│   ├── export-data.js     # 导出数据脚本
│   ├── import-data.js     # 导入数据脚本
│   ├── clear-data.js      # 清空数据脚本
│   └── quick-start.js     # 快速启动脚本
├── test-db.js            # 数据库连接测试
└── seed-test-data.js     # 测试数据生成
```

## 🔧 故障排除

### 问题1: Prisma Studio 显示空数据
**解决方案**:
```bash
npm run db:test           # 检查连接状态
npm run db:export         # 导出当前数据
npm run db:import <文件名> # 重新导入数据
```

### 问题2: 数据库连接失败
**解决方案**:
1. 检查 `.env` 文件中的 `DATABASE_URL`
2. 确认 Neon 数据库状态
3. 检查网络连接

### 问题3: 端口被占用
**解决方案**:
```bash
# 查找占用端口的进程
netstat -ano | findstr :3000
# 杀死进程
taskkill /PID <PID> /F
```

## 📝 数据同步说明

- ✅ **本地 → Neon Console**: 自动同步（实时）
- ✅ **Neon Console → 本地**: 自动同步（实时）
- 📊 **数据一致性**: 本地和云端使用同一数据库实例

## 🎯 开发建议

1. **每日启动**: 使用 `npm run quick-start` 一键启动
2. **定期备份**: 重要操作前运行 `npm run db:export`
3. **数据验证**: 使用 `npm run db:test` 检查数据状态
4. **清理测试**: 开发测试后使用 `npm run db:clear` 清理

---

📖 **详细文档**: 查看 `DATABASE_OPERATIONS.md` 获取完整操作指南
