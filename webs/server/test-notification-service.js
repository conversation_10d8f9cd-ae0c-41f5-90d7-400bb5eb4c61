require('dotenv').config();
const notificationService = require('./src/services/notificationService');

async function testNotificationService() {
  console.log('🧪 测试通知服务...');

  try {
    // 1. 测试推送给用户
    console.log('1️⃣ 测试推送给用户...');
    
    // 首先我们需要一个用户ID，让我们创建一个测试用户
    const { PrismaClient } = require('@prisma/client');
    const prisma = new PrismaClient();
    
    // 创建测试用户
    const testUser = await prisma.user.create({
      data: {
        name: '通知测试用户',
        phone: `139${Math.floor(Math.random() * 100000000).toString().padStart(8, '0')}`,
        password: 'test123'
      }
    });
    
    console.log('✅ 测试用户创建成功:', testUser.name);
    
    // 测试推送通知
    const notification = await notificationService.pushToUser(
      testUser.id,
      '这是一条测试通知',
      'test'
    );
    
    console.log('✅ 通知推送成功:', notification.content);
    
    // 2. 测试获取未读数量
    console.log('\n2️⃣ 测试获取未读数量...');
    const unreadCount = await notificationService.getUnreadCount(testUser.id);
    console.log('✅ 未读数量:', unreadCount);
    
    // 3. 测试家庭推送
    console.log('\n3️⃣ 测试家庭推送...');
    const familyNotifications = await notificationService.pushToFamily(
      '这是一条家庭通知测试',
      'family_test',
      testUser.id
    );
    console.log('✅ 家庭通知推送成功，数量:', familyNotifications.length);
    
    // 4. 测试智能推送 - 新菜品
    console.log('\n4️⃣ 测试新菜品智能推送...');
    const mockDish = {
      name: '测试菜品',
      description: '这是一道测试菜品'
    };
    
    const mockCreator = {
      id: testUser.id,
      name: testUser.name
    };
    
    const dishNotifications = await notificationService.notifyNewDish(mockDish, mockCreator);
    console.log('✅ 新菜品通知推送成功，数量:', dishNotifications.length);
    
    // 5. 清理测试数据
    console.log('\n5️⃣ 清理测试数据...');
    await prisma.notification.deleteMany({
      where: { userId: testUser.id }
    });
    await prisma.user.delete({
      where: { id: testUser.id }
    });
    console.log('✅ 测试数据清理完成');
    
    console.log('\n🎉 通知服务测试完成！');
    
  } catch (error) {
    console.error('❌ 通知服务测试失败:', error);
  }
}

// 运行测试
testNotificationService();
