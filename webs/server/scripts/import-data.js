require('dotenv').config();
const { PrismaClient } = require('@prisma/client');
const fs = require('fs');
const path = require('path');

const prisma = new PrismaClient();

async function importData(filename) {
  try {
    console.log('📥 开始导入数据...');
    
    if (!filename) {
      console.log('❌ 请提供导入文件名');
      console.log('用法: node import-data.js <filename>');
      return;
    }

    const filepath = path.join(__dirname, '../exports', filename);
    
    if (!fs.existsSync(filepath)) {
      console.log(`❌ 文件不存在: ${filepath}`);
      return;
    }

    const importData = JSON.parse(fs.readFileSync(filepath, 'utf8'));
    console.log(`📁 读取文件: ${filename}`);
    console.log(`📅 导出时间: ${importData.timestamp}`);
    console.log(`🗄️ 源数据库: ${importData.database}`);

    // 清空现有数据（按依赖关系顺序）
    console.log('\n🗑️ 清空现有数据...');
    const clearOrder = ['menuItem', 'menu', 'dish', 'category', 'order', 'message', 'notification', 'user'];
    
    for (const table of clearOrder) {
      try {
        const result = await prisma[table].deleteMany();
        console.log(`   清空 ${table}: ${result.count} 条记录`);
      } catch (error) {
        console.log(`   清空 ${table} 失败:`, error.message);
      }
    }

    // 导入数据（按依赖关系顺序）
    console.log('\n📥 导入新数据...');
    const importOrder = ['user', 'category', 'dish', 'menu', 'menuItem', 'order', 'message', 'notification'];
    
    for (const table of importOrder) {
      try {
        const data = importData.data[table] || [];
        if (data.length > 0) {
          // 批量创建数据
          for (const record of data) {
            await prisma[table].create({ data: record });
          }
          console.log(`   导入 ${table}: ${data.length} 条记录`);
        } else {
          console.log(`   跳过 ${table}: 无数据`);
        }
      } catch (error) {
        console.log(`   导入 ${table} 失败:`, error.message);
      }
    }

    console.log('\n🎉 数据导入完成！');
    
    // 验证导入结果
    console.log('\n📊 导入验证:');
    for (const table of importOrder) {
      try {
        const count = await prisma[table].count();
        console.log(`   ${table}: ${count} 条记录`);
      } catch (error) {
        console.log(`   ${table}: 验证失败`);
      }
    }

  } catch (error) {
    console.error('❌ 导入失败:', error);
  } finally {
    await prisma.$disconnect();
  }
}

// 从命令行参数获取文件名
const filename = process.argv[2];
importData(filename);
