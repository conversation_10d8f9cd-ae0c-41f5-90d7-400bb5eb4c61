// 简单测试脚本，不依赖 dotenv
// 手动设置环境变量
process.env.PICX_TOKEN = '****************************************';
process.env.PICX_REPO = '/repos/2497462726/picx-images-hosting/contents/';

const fs = require('fs');
const path = require('path');

// 导入 picxService
const picxService = require('../src/services/picxService');

// 创建一个简单的测试图片
function createTestImage() {
  // 创建一个简单的 1x1 像素的黑色 PNG 图片
  const buffer = Buffer.from([
    0x89, 0x50, 0x4e, 0x47, 0x0d, 0x0a, 0x1a, 0x0a, 0x00, 0x00, 0x00, 0x0d,
    0x49, 0x48, 0x44, 0x52, 0x00, 0x00, 0x00, 0x01, 0x00, 0x00, 0x00, 0x01,
    0x08, 0x06, 0x00, 0x00, 0x00, 0x1f, 0x15, 0xc4, 0x89, 0x00, 0x00, 0x00,
    0x0a, 0x49, 0x44, 0x41, 0x54, 0x78, 0x9c, 0x63, 0x00, 0x01, 0x00, 0x00,
    0x05, 0x00, 0x01, 0x0d, 0x0a, 0x2d, 0xb4, 0x00, 0x00, 0x00, 0x00, 0x49,
    0x45, 0x4e, 0x44, 0xae, 0x42, 0x60, 0x82
  ]);

  // 保存到临时文件
  const tempPath = path.join(__dirname, 'test.png');
  fs.writeFileSync(tempPath, buffer);
  console.log(`创建测试图片: ${tempPath}`);

  return {path: tempPath, buffer};
}

// 测试图片上传功能
async function testImageUpload() {
  try {
    console.log('开始测试图片上传...');

    // 创建测试图片
    const testImage = createTestImage();

    // 上传图片
    console.log('正在上传图片...');
    const imageUrl = await picxService.uploadImage(
      testImage.buffer,
      'test.png'
    );

    console.log('图片上传成功!');
    console.log(`图片URL: ${imageUrl}`);

    // 清理临时文件
    fs.unlinkSync(testImage.path);
  } catch (error) {
    console.error('图片上传测试失败:', error);
  }
}

// 执行测试
testImageUpload();
