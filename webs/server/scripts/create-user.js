require('dotenv').config();
const { PrismaClient } = require('@prisma/client');
const bcrypt = require('bcrypt');

const prisma = new PrismaClient();

async function createUser() {
  try {
    // 检查用户是否已存在
    const existingUser = await prisma.user.findFirst({
      where: {
        OR: [
          { phone: '13800138000' },
          { name: 'admin' }
        ]
      }
    });

    if (existingUser) {
      console.log('User already exists:', existingUser.name);
      return existingUser;
    }

    // 创建密码哈希
    const saltRounds = 10;
    const hashedPassword = await bcrypt.hash('password123', saltRounds);

    // 创建用户
    const user = await prisma.user.create({
      data: {
        name: 'admin',
        phone: '13800138000',
        password: hashedPassword,
        role: 'ADMIN'
      }
    });

    console.log('User created successfully:', user);
    return user;
  } catch (error) {
    console.error('Error creating user:', error);
    throw error;
  } finally {
    await prisma.$disconnect();
  }
}

createUser()
  .then(() => {
    console.log('Script completed');
    process.exit(0);
  })
  .catch((error) => {
    console.error('Script failed:', error);
    process.exit(1);
  });
