require('dotenv').config();
const { PrismaClient } = require('@prisma/client');
const fs = require('fs');
const path = require('path');

const prisma = new PrismaClient();

async function exportData() {
  try {
    console.log('📤 开始导出数据...');
    
    const exportData = {
      timestamp: new Date().toISOString(),
      database: process.env.DATABASE_URL?.split('/').pop()?.split('?')[0],
      data: {}
    };

    // 导出所有表的数据
    const tables = ['user', 'category', 'dish', 'menu', 'menuItem', 'order', 'message', 'notification'];
    
    for (const table of tables) {
      try {
        const data = await prisma[table].findMany();
        exportData.data[table] = data;
        console.log(`✅ 导出 ${table}: ${data.length} 条记录`);
      } catch (error) {
        console.log(`❌ 导出 ${table} 失败:`, error.message);
        exportData.data[table] = [];
      }
    }

    // 保存到文件
    const exportDir = path.join(__dirname, '../exports');
    if (!fs.existsSync(exportDir)) {
      fs.mkdirSync(exportDir, { recursive: true });
    }

    const filename = `data-export-${new Date().toISOString().replace(/[:.]/g, '-')}.json`;
    const filepath = path.join(exportDir, filename);
    
    fs.writeFileSync(filepath, JSON.stringify(exportData, null, 2));
    
    console.log(`🎉 数据导出完成！`);
    console.log(`📁 文件位置: ${filepath}`);
    
    // 显示统计信息
    console.log('\n📊 导出统计:');
    for (const [table, data] of Object.entries(exportData.data)) {
      console.log(`   ${table}: ${data.length} 条记录`);
    }

  } catch (error) {
    console.error('❌ 导出失败:', error);
  } finally {
    await prisma.$disconnect();
  }
}

exportData();
