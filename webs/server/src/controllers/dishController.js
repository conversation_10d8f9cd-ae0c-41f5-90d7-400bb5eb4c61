const prisma = require('../utils/prisma');
const {success, error} = require('../utils/response');
const notificationService = require('../services/notificationService');

/**
 * 获取菜品列表（支持分页和搜索）
 * @route GET /api/dishes
 */
const getDishes = async (req, res) => {
  try {
    const {
      page = 1,
      size = 10,
      categoryId,
      name,
      category,
      isAvailable,
      search = '',
      sortBy = 'createdAt',
      sortOrder = 'desc'
    } = req.query;

    // 构建查询条件
    const where = {};

    if (categoryId) {
      where.categoryId = categoryId;
    }

    if (name) {
      where.name = {
        contains: name,
        mode: 'insensitive'
      };
    }

    if (search) {
      where.OR = [
        {name: {contains: search, mode: 'insensitive'}},
        {description: {contains: search, mode: 'insensitive'}}
      ];
    }

    if (category) {
      where.category = {
        name: {
          contains: category,
          mode: 'insensitive'
        }
      };
    }

    if (isAvailable !== undefined) {
      where.isAvailable = isAvailable === 'true';
    }

    // 计算分页
    const skip = (parseInt(page) - 1) * parseInt(size);
    const take = parseInt(size);

    // 获取总数
    const total = await prisma.dish.count({where});

    // 获取菜品列表
    const dishes = await prisma.dish.findMany({
      where,
      include: {
        category: true,
        _count: {
          select: {
            menuItems: true
          }
        }
      },
      orderBy: {
        [sortBy]: sortOrder
      },
      skip,
      take
    });

    // 格式化数据为后台管理系统需要的格式
    const formattedDishes = dishes.map(dish => ({
      id: dish.id,
      name: dish.name,
      category: dish.category?.name || '未分类',
      categoryId: dish.categoryId,
      description: dish.description,
      ingredients: dish.ingredients,
      cookingMethod: dish.cookingMethod,
      image: dish.image,
      isAvailable: dish.isAvailable,
      usageCount: dish._count.menuItems,
      createdAt: dish.createdAt,
      updatedAt: dish.updatedAt
    }));

    return success(res, {
      list: formattedDishes,
      total,
      page: parseInt(page),
      size: parseInt(size),
      totalPages: Math.ceil(total / parseInt(size))
    });
  } catch (err) {
    console.error('Get dishes error:', err);
    return error(res, 'Failed to get dishes', 500);
  }
};

/**
 * 获取菜品分类
 * @route GET /api/dishes/categories
 */
const getCategories = async (req, res) => {
  try {
    const categories = await prisma.category.findMany({
      orderBy: {
        createdAt: 'asc'
      }
    });

    return success(res, categories);
  } catch (err) {
    console.error('Get categories error:', err);
    return error(res, 'Failed to get categories', 500);
  }
};

/**
 * 获取指定菜品
 * @route GET /api/dishes/:id
 */
const getDishById = async (req, res) => {
  try {
    const {id} = req.params;

    const dish = await prisma.dish.findUnique({
      where: {id},
      include: {
        category: true
      }
    });

    if (!dish) {
      return error(res, 'Dish not found', 404);
    }

    return success(res, dish);
  } catch (err) {
    console.error('Get dish error:', err);
    return error(res, 'Failed to get dish', 500);
  }
};

/**
 * 创建菜品
 * @route POST /api/dishes
 */
const createDish = async (req, res) => {
  try {
    const {name, description, image, category} = req.body;

    if (!name || !category) {
      return error(res, 'Name and category are required', 400);
    }

    // 查找或创建分类
    let categoryRecord = await prisma.category.findFirst({
      where: {name: category}
    });

    if (!categoryRecord) {
      categoryRecord = await prisma.category.create({
        data: {name: category}
      });
    }

    // 检查菜品名称是否已存在
    const existingDish = await prisma.dish.findFirst({
      where: {
        name,
        categoryId: categoryRecord.id
      }
    });

    if (existingDish) {
      return error(
        res,
        'Dish with this name already exists in this category',
        400
      );
    }

    // 创建菜品
    const dish = await prisma.dish.create({
      data: {
        name,
        description,
        image,
        categoryId: categoryRecord.id
      },
      include: {
        category: true
      }
    });

    // 发送智能推送通知
    try {
      await notificationService.notifyNewDish(dish, req.user);
    } catch (notifyError) {
      console.error('Failed to send notification:', notifyError);
      // 不影响主要功能，只记录错误
    }

    return success(res, dish, 'Dish created successfully', 201);
  } catch (err) {
    console.error('Create dish error:', err);
    return error(res, 'Failed to create dish', 500);
  }
};

/**
 * 更新菜品
 * @route PUT /api/dishes/:id
 */
const updateDish = async (req, res) => {
  try {
    const {id} = req.params;
    const {name, description, image, category} = req.body;

    // 检查菜品是否存在
    const existingDish = await prisma.dish.findUnique({
      where: {id}
    });

    if (!existingDish) {
      return error(res, 'Dish not found', 404);
    }

    // 构建更新数据
    const updateData = {};

    if (name !== undefined) updateData.name = name;
    if (description !== undefined) updateData.description = description;
    if (image !== undefined) updateData.image = image;

    // 如果更新分类
    if (category) {
      let categoryRecord = await prisma.category.findFirst({
        where: {name: category}
      });

      if (!categoryRecord) {
        categoryRecord = await prisma.category.create({
          data: {name: category}
        });
      }

      updateData.categoryId = categoryRecord.id;
    }

    // 更新菜品
    const updatedDish = await prisma.dish.update({
      where: {id},
      data: updateData,
      include: {
        category: true
      }
    });

    return success(res, updatedDish, 'Dish updated successfully');
  } catch (err) {
    console.error('Update dish error:', err);
    return error(res, 'Failed to update dish', 500);
  }
};

/**
 * 删除菜品
 * @route DELETE /api/dishes/:id
 */
const deleteDish = async (req, res) => {
  try {
    const {id} = req.params;

    // 检查菜品是否存在
    const existingDish = await prisma.dish.findUnique({
      where: {id}
    });

    if (!existingDish) {
      return error(res, 'Dish not found', 404);
    }

    // 检查菜品是否被菜单引用
    const menuItems = await prisma.menuItem.findMany({
      where: {dishId: id}
    });

    if (menuItems.length > 0) {
      return error(res, 'Cannot delete dish that is referenced in menus', 400);
    }

    // 删除菜品
    await prisma.dish.delete({
      where: {id}
    });

    return success(res, null, 'Dish deleted successfully');
  } catch (err) {
    console.error('Delete dish error:', err);
    return error(res, 'Failed to delete dish', 500);
  }
};

/**
 * 创建菜品分类
 * @route POST /api/dishes/categories
 */
const createCategory = async (req, res) => {
  try {
    const {name} = req.body;

    if (!name) {
      return error(res, 'Name is required', 400);
    }

    // 创建分类
    const category = await prisma.category.create({
      data: {name}
    });

    return success(res, category, 'Category created successfully', 201);
  } catch (err) {
    console.error('Create category error:', err);
    return error(res, 'Failed to create category', 500);
  }
};

/**
 * 更新菜品分类
 * @route PUT /api/dishes/categories/:id
 */
const updateCategory = async (req, res) => {
  try {
    const {id} = req.params;
    const {name} = req.body;

    if (!name) {
      return error(res, 'Name is required', 400);
    }

    // 检查分类是否存在
    const existingCategory = await prisma.category.findUnique({
      where: {id}
    });

    if (!existingCategory) {
      return error(res, 'Category not found', 404);
    }

    // 更新分类
    const updatedCategory = await prisma.category.update({
      where: {id},
      data: {name}
    });

    return success(res, updatedCategory, 'Category updated successfully');
  } catch (err) {
    console.error('Update category error:', err);
    return error(res, 'Failed to update category', 500);
  }
};

/**
 * 删除菜品分类
 * @route DELETE /api/dishes/categories/:id
 */
const deleteCategory = async (req, res) => {
  try {
    const {id} = req.params;

    // 检查分类是否存在
    const existingCategory = await prisma.category.findUnique({
      where: {id}
    });

    if (!existingCategory) {
      return error(res, 'Category not found', 404);
    }

    // 检查分类是否被菜品引用
    const dishes = await prisma.dish.findMany({
      where: {categoryId: id}
    });

    if (dishes.length > 0) {
      return error(res, 'Cannot delete category that contains dishes', 400);
    }

    // 删除分类
    await prisma.category.delete({
      where: {id}
    });

    return success(res, null, 'Category deleted successfully');
  } catch (err) {
    console.error('Delete category error:', err);
    return error(res, 'Failed to delete category', 500);
  }
};

/**
 * 获取分类菜品（为小程序优化）
 * @route GET /api/dishes/by-category
 */
const getDishesByCategory = async (req, res) => {
  try {
    const categories = await prisma.category.findMany({
      include: {
        dishes: {
          orderBy: {
            createdAt: 'desc'
          }
        }
      },
      orderBy: {
        createdAt: 'asc'
      }
    });

    // 格式化为小程序需要的数据结构
    const formattedData = {};

    categories.forEach(category => {
      // 使用分类名称的拼音或英文作为key
      let categoryKey = '';
      switch (category.name) {
        case '热菜':
          categoryKey = 'hot';
          break;
        case '凉菜':
          categoryKey = 'cold';
          break;
        case '汤品':
          categoryKey = 'soup';
          break;
        case '主食':
          categoryKey = 'staple';
          break;
        case '甜品':
          categoryKey = 'dessert';
          break;
        default:
          categoryKey = category.name.toLowerCase();
      }

      formattedData[categoryKey] = category.dishes.map(dish => ({
        id: dish.id,
        name: dish.name,
        img:
          dish.image ||
          'https://cdn.pixabay.com/photo/2017/05/07/08/56/food-2290814_1280.jpg',
        remark: dish.description || '美味可口',
        material: '新鲜食材精心制作',
        method: '传统工艺，营养健康'
      }));
    });

    return success(res, formattedData);
  } catch (err) {
    console.error('Get dishes by category error:', err);
    return error(res, 'Failed to get dishes by category', 500);
  }
};

/**
 * 获取菜品详情（为小程序优化）
 * @route GET /api/dishes/:id/detail
 */
const getDishDetail = async (req, res) => {
  try {
    const {id} = req.params;

    const dish = await prisma.dish.findUnique({
      where: {id},
      include: {
        category: true
      }
    });

    if (!dish) {
      return error(res, 'Dish not found', 404);
    }

    // 格式化为小程序详情页需要的数据
    const dishDetail = {
      id: dish.id,
      name: dish.name,
      img:
        dish.image ||
        'https://cdn.pixabay.com/photo/2017/05/07/08/56/food-2290814_1280.jpg',
      remark: dish.description || '美味可口，营养丰富',
      material: '新鲜食材，精心挑选',
      method: '1. 准备新鲜食材；2. 传统工艺制作；3. 营养搭配均衡。',
      category: dish.category.name
    };

    return success(res, dishDetail);
  } catch (err) {
    console.error('Get dish detail error:', err);
    return error(res, 'Failed to get dish detail', 500);
  }
};

/**
 * 获取菜品统计信息
 * @route GET /api/dishes/statistics
 */
const getDishStatistics = async (req, res) => {
  try {
    // 总菜品数
    const totalDishes = await prisma.dish.count();

    // 分类统计
    const categoryStats = await prisma.category.findMany({
      include: {
        _count: {
          select: {dishes: true}
        }
      }
    });

    // 最近添加的菜品
    const recentDishes = await prisma.dish.findMany({
      take: 5,
      orderBy: {createdAt: 'desc'},
      include: {category: true}
    });

    return success(res, {
      totalDishes,
      availableDishes: totalDishes, // 所有菜品都可用
      unavailableDishes: 0,
      categoryStats: categoryStats.map(cat => ({
        name: cat.name,
        count: cat._count.dishes
      })),
      recentDishes: recentDishes.map(dish => ({
        id: dish.id,
        name: dish.name,
        category: dish.category?.name,
        createdAt: dish.createdAt
      }))
    });
  } catch (err) {
    console.error('Get dish statistics error:', err);
    return error(res, 'Failed to get dish statistics', 500);
  }
};

/**
 * 获取热门菜品
 * @route GET /api/dishes/hot
 */
const getHotDishes = async (req, res) => {
  try {
    const {limit = 10} = req.query;

    // 这里可以根据订单数据来统计热门菜品
    // 暂时返回最新的菜品作为热门菜品
    const hotDishes = await prisma.dish.findMany({
      include: {category: true},
      orderBy: {createdAt: 'desc'},
      take: parseInt(limit)
    });

    return success(
      res,
      hotDishes.map(dish => ({
        id: dish.id,
        name: dish.name,
        category: dish.category?.name,
        description: dish.description,
        image: dish.image,
        orderCount: Math.floor(Math.random() * 100) + 10 // 模拟订单数
      }))
    );
  } catch (err) {
    console.error('Get hot dishes error:', err);
    return error(res, 'Failed to get hot dishes', 500);
  }
};

/**
 * 批量操作菜品
 * @route POST /api/dishes/batch
 */
const batchOperation = async (req, res) => {
  try {
    const {operation, dishIds} = req.body;

    if (!operation || !dishIds || !Array.isArray(dishIds)) {
      return error(res, 'Operation and dishIds are required', 400);
    }

    let result;

    switch (operation) {
      case 'delete':
        result = await prisma.dish.deleteMany({
          where: {id: {in: dishIds}}
        });
        break;
      default:
        return error(res, 'Invalid operation', 400);
    }

    return success(res, result, `Batch ${operation} completed successfully`);
  } catch (err) {
    console.error('Batch operation error:', err);
    return error(res, 'Failed to perform batch operation', 500);
  }
};

module.exports = {
  getDishes,
  getCategories,
  getDishById,
  createDish,
  updateDish,
  deleteDish,
  createCategory,
  updateCategory,
  deleteCategory,
  getDishesByCategory,
  getDishDetail,
  getDishStatistics,
  getHotDishes,
  batchOperation
};
