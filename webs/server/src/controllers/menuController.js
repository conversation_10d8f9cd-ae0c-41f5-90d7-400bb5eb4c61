const prisma = require('../utils/prisma');
const {success, error} = require('../utils/response');

/**
 * 获取菜单列表
 * @route GET /api/menus
 */
const getMenus = async (req, res) => {
  try {
    // 分页参数
    const page = parseInt(req.query.page, 10) || 1;
    const size = parseInt(req.query.size, 10) || 10;
    const skip = (page - 1) * size;
    const take = size;

    // 搜索参数
    const {date, isToday} = req.query;
    const where = {};
    if (date) {
      // 支持模糊匹配或精确匹配
      where.date = new Date(date);
    }
    if (isToday !== undefined && isToday !== '') {
      where.isToday = isToday === 'true' || isToday === true;
    }

    // 获取总数
    const total = await prisma.menu.count({where});

    // 获取菜单数据
    const menus = await prisma.menu.findMany({
      where,
      include: {
        items: {
          include: {
            dish: true
          }
        }
      },
      orderBy: {
        date: 'desc'
      },
      skip,
      take
    });

    // 格式化菜单数据
    const list = menus.map(menu => ({
      id: menu.id,
      date: menu.date,
      remark: menu.remark,
      isToday: menu.isToday,
      dishes: menu.items.map(item => ({
        id: item.dish.id,
        name: item.dish.name,
        description: item.dish.description,
        image: item.dish.image,
        count: item.count
      }))
    }));

    return success(res, {list, total});
  } catch (err) {
    console.error('Get menus error:', err);
    return error(res, 'Failed to get menus', 500);
  }
};

/**
 * 获取今日菜单
 * @route GET /api/menus/today
 */
const getTodayMenu = async (req, res) => {
  try {
    // 获取今天的日期（不包含时间）
    const today = new Date();
    today.setHours(0, 0, 0, 0);

    const tomorrow = new Date(today);
    tomorrow.setDate(tomorrow.getDate() + 1);

    // 查找今日菜单
    const todayMenu = await prisma.menu.findFirst({
      where: {
        date: {
          gte: today,
          lt: tomorrow
        },
        isToday: true
      },
      include: {
        items: {
          include: {
            dish: true
          }
        }
      }
    });

    if (!todayMenu) {
      return success(res, null, 'No menu for today');
    }

    // 格式化菜单数据
    const formattedMenu = {
      id: todayMenu.id,
      date: todayMenu.date,
      remark: todayMenu.remark,
      dishes: todayMenu.items.map(item => ({
        id: item.dish.id,
        name: item.dish.name,
        description: item.dish.description,
        image: item.dish.image,
        count: item.count
      }))
    };

    return success(res, formattedMenu);
  } catch (err) {
    console.error('Get today menu error:', err);
    return error(res, 'Failed to get today menu', 500);
  }
};

/**
 * 获取历史菜单
 * @route GET /api/menus/history
 */
const getHistoryMenus = async (req, res) => {
  try {
    const historyMenus = await prisma.menu.findMany({
      where: {
        isToday: false
      },
      include: {
        items: {
          include: {
            dish: true
          }
        }
      },
      orderBy: {
        date: 'desc'
      },
      take: 10
    });

    // 格式化菜单数据
    const formattedMenus = historyMenus.map(menu => ({
      id: menu.id,
      date: menu.date,
      remark: menu.remark,
      dishes: menu.items.map(item => ({
        id: item.dish.id,
        name: item.dish.name,
        description: item.dish.description,
        image: item.dish.image,
        count: item.count
      }))
    }));

    return success(res, formattedMenus);
  } catch (err) {
    console.error('Get history menus error:', err);
    return error(res, 'Failed to get history menus', 500);
  }
};

/**
 * 获取指定菜单
 * @route GET /api/menus/:id
 */
const getMenuById = async (req, res) => {
  try {
    const {id} = req.params;

    const menu = await prisma.menu.findUnique({
      where: {id},
      include: {
        items: {
          include: {
            dish: true
          }
        }
      }
    });

    if (!menu) {
      return error(res, 'Menu not found', 404);
    }

    // 格式化菜单数据
    const formattedMenu = {
      id: menu.id,
      date: menu.date,
      remark: menu.remark,
      dishes: menu.items.map(item => ({
        id: item.dish.id,
        name: item.dish.name,
        description: item.dish.description,
        image: item.dish.image,
        count: item.count
      }))
    };

    return success(res, formattedMenu);
  } catch (err) {
    console.error('Get menu error:', err);
    return error(res, 'Failed to get menu', 500);
  }
};

/**
 * 创建菜单
 * @route POST /api/menus
 */
const createMenu = async (req, res) => {
  try {
    const {date, dishes, remark, isToday} = req.body;

    if (!date || !dishes || !Array.isArray(dishes) || dishes.length === 0) {
      return error(res, 'Date and dishes are required', 400);
    }

    // 创建菜单
    const menu = await prisma.menu.create({
      data: {
        date: new Date(date),
        remark,
        isToday: isToday || false
      }
    });

    // 创建菜单项
    const menuItems = [];

    for (const dish of dishes) {
      // 检查菜品是否存在
      const existingDish = await prisma.dish.findUnique({
        where: {id: dish.id}
      });

      if (!existingDish) {
        continue; // 跳过不存在的菜品
      }

      const menuItem = await prisma.menuItem.create({
        data: {
          menuId: menu.id,
          dishId: dish.id,
          count: dish.count || 1
        }
      });

      menuItems.push(menuItem);
    }

    // 如果是今日菜单，将其他菜单的 isToday 设为 false
    if (isToday) {
      await prisma.menu.updateMany({
        where: {
          id: {not: menu.id},
          isToday: true
        },
        data: {
          isToday: false
        }
      });
    }

    return success(
      res,
      {
        id: menu.id,
        date: menu.date,
        remark: menu.remark,
        isToday: menu.isToday,
        items: menuItems
      },
      'Menu created successfully',
      201
    );
  } catch (err) {
    console.error('Create menu error:', err);
    return error(res, 'Failed to create menu', 500);
  }
};

/**
 * 更新菜单
 * @route PUT /api/menus/:id
 */
const updateMenu = async (req, res) => {
  try {
    const {id} = req.params;
    const {date, dishes, remark, isToday} = req.body;

    // 检查菜单是否存在
    const existingMenu = await prisma.menu.findUnique({
      where: {id}
    });

    if (!existingMenu) {
      return error(res, 'Menu not found', 404);
    }

    // 更新菜单
    const updateData = {};

    if (date) updateData.date = new Date(date);
    if (remark !== undefined) updateData.remark = remark;
    if (isToday !== undefined) updateData.isToday = isToday;

    const updatedMenu = await prisma.menu.update({
      where: {id},
      data: updateData
    });

    // 如果提供了菜品，更新菜单项
    if (dishes && Array.isArray(dishes)) {
      // 删除现有菜单项
      await prisma.menuItem.deleteMany({
        where: {menuId: id}
      });

      // 创建新菜单项
      for (const dish of dishes) {
        // 检查菜品是否存在
        const existingDish = await prisma.dish.findUnique({
          where: {id: dish.id}
        });

        if (!existingDish) {
          continue; // 跳过不存在的菜品
        }

        await prisma.menuItem.create({
          data: {
            menuId: id,
            dishId: dish.id,
            count: dish.count || 1
          }
        });
      }
    }

    // 如果是今日菜单，将其他菜单的 isToday 设为 false
    if (isToday) {
      await prisma.menu.updateMany({
        where: {
          id: {not: id},
          isToday: true
        },
        data: {
          isToday: false
        }
      });
    }

    return success(res, updatedMenu, 'Menu updated successfully');
  } catch (err) {
    console.error('Update menu error:', err);
    return error(res, 'Failed to update menu', 500);
  }
};

/**
 * 删除菜单
 * @route DELETE /api/menus/:id
 */
const deleteMenu = async (req, res) => {
  try {
    const {id} = req.params;

    // 检查菜单是否存在
    const existingMenu = await prisma.menu.findUnique({
      where: {id}
    });

    if (!existingMenu) {
      return error(res, 'Menu not found', 404);
    }

    // 删除菜单项
    await prisma.menuItem.deleteMany({
      where: {menuId: id}
    });

    // 删除菜单
    await prisma.menu.delete({
      where: {id}
    });

    return success(res, null, 'Menu deleted successfully');
  } catch (err) {
    console.error('Delete menu error:', err);
    return error(res, 'Failed to delete menu', 500);
  }
};

/**
 * 获取菜品分类
 * @route GET /api/menus/categories
 */
const getCategories = async (req, res) => {
  try {
    const categories = await prisma.category.findMany({
      include: {
        dishes: {
          select: {
            id: true,
            name: true,
            description: true,
            image: true
          }
        }
      },
      orderBy: {
        createdAt: 'asc'
      }
    });

    return success(res, categories);
  } catch (err) {
    console.error('Get categories error:', err);
    return error(res, 'Failed to get categories', 500);
  }
};

/**
 * 获取推荐菜单
 * @route GET /api/menus/recommended
 */
const getRecommendedMenu = async (req, res) => {
  try {
    // 获取最受欢迎的菜品（基于菜单项的数量）
    const popularDishes = await prisma.dish.findMany({
      include: {
        menuItems: true,
        category: true
      },
      orderBy: {
        createdAt: 'desc'
      },
      take: 6
    });

    // 格式化推荐菜品
    const recommendedDishes = popularDishes.map(dish => ({
      id: dish.id,
      name: dish.name,
      description: dish.description,
      image: dish.image,
      category: dish.category.name,
      count: dish.menuItems.length || 0
    }));

    return success(res, recommendedDishes);
  } catch (err) {
    console.error('Get recommended menu error:', err);
    return error(res, 'Failed to get recommended menu', 500);
  }
};

/**
 * 获取菜品统计信息
 * @route GET /api/menus/statistics
 */
const getStatistics = async (req, res) => {
  try {
    // 获取今日订单数
    const today = new Date();
    today.setHours(0, 0, 0, 0);
    const tomorrow = new Date(today);
    tomorrow.setDate(tomorrow.getDate() + 1);

    // 今日订单数
    const todayOrders = await prisma.order.count({
      where: {
        createdAt: {
          gte: today,
          lt: tomorrow
        }
      }
    });

    // 菜品总数
    const totalDishes = await prisma.dish.count();

    // 活跃用户（本月下单用户数）
    const monthStart = new Date(today.getFullYear(), today.getMonth(), 1);
    const activeUsers = await prisma.order.findMany({
      where: {
        createdAt: {
          gte: monthStart,
          lt: tomorrow
        }
      },
      select: {userId: true}
    });
    const activeUserCount = new Set(activeUsers.map(u => u.userId)).size;

    // 月访问量（模拟）
    const monthlyVisits = Math.floor(Math.random() * 500) + 200;

    return success(res, {
      todayOrders,
      totalDishes,
      activeUsers: activeUserCount,
      monthlyVisits
    });
  } catch (err) {
    console.error('Get statistics error:', err);
    return error(res, 'Failed to get statistics', 500);
  }
};

module.exports = {
  getMenus,
  getTodayMenu,
  getHistoryMenus,
  getMenuById,
  createMenu,
  updateMenu,
  deleteMenu,
  getCategories,
  getRecommendedMenu,
  getStatistics
};
