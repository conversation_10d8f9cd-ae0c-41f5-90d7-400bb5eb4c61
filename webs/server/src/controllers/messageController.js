const prisma = require('../utils/prisma');
const {success, error} = require('../utils/response');

/**
 * 获取消息列表（支持分页和搜索）
 * @route GET /api/messages
 */
const getMessages = async (req, res) => {
  try {
    const {
      page = 1,
      size = 10,
      userId,
      read,
      content,
      startDate,
      endDate
    } = req.query;

    // 构建查询条件
    const where = {};

    if (userId) where.userId = userId;
    if (read !== undefined) where.read = read === 'true';

    if (content) {
      where.content = {
        contains: content,
        mode: 'insensitive'
      };
    }

    if (startDate || endDate) {
      where.createdAt = {};
      if (startDate) where.createdAt.gte = new Date(startDate);
      if (endDate) {
        const endDateTime = new Date(endDate);
        endDateTime.setHours(23, 59, 59, 999);
        where.createdAt.lte = endDateTime;
      }
    }

    // 计算分页
    const skip = (parseInt(page) - 1) * parseInt(size);
    const take = parseInt(size);

    // 获取总数
    const total = await prisma.message.count({where});

    // 获取消息列表
    const messages = await prisma.message.findMany({
      where,
      include: {
        user: {
          select: {
            id: true,
            name: true,
            avatar: true
          }
        }
      },
      orderBy: {
        createdAt: 'desc'
      },
      skip,
      take
    });

    return success(res, {
      list: messages,
      total,
      page: parseInt(page),
      size: parseInt(size),
      totalPages: Math.ceil(total / parseInt(size))
    });
  } catch (err) {
    console.error('Get messages error:', err);
    return error(res, 'Failed to get messages', 500);
  }
};

/**
 * 获取指定消息
 * @route GET /api/messages/:id
 */
const getMessageById = async (req, res) => {
  try {
    const {id} = req.params;

    const message = await prisma.message.findUnique({
      where: {id},
      include: {
        user: {
          select: {
            id: true,
            name: true,
            avatar: true
          }
        }
      }
    });

    if (!message) {
      return error(res, 'Message not found', 404);
    }

    return success(res, message);
  } catch (err) {
    console.error('Get message error:', err);
    return error(res, 'Failed to get message', 500);
  }
};

/**
 * 创建消息
 * @route POST /api/messages
 */
const createMessage = async (req, res) => {
  try {
    const {content} = req.body;
    const userId = req.user.id;

    if (!content) {
      return error(res, 'Content is required', 400);
    }

    // 创建消息
    const message = await prisma.message.create({
      data: {
        content,
        userId,
        read: false
      },
      include: {
        user: {
          select: {
            id: true,
            name: true,
            avatar: true
          }
        }
      }
    });

    return success(res, message, 'Message created successfully', 201);
  } catch (err) {
    console.error('Create message error:', err);
    return error(res, 'Failed to create message', 500);
  }
};

/**
 * 更新消息
 * @route PUT /api/messages/:id
 */
const updateMessage = async (req, res) => {
  try {
    const {id} = req.params;
    const {content, read} = req.body;

    // 检查消息是否存在
    const existingMessage = await prisma.message.findUnique({
      where: {id}
    });

    if (!existingMessage) {
      return error(res, 'Message not found', 404);
    }

    // 检查权限（只有管理员或消息所有者可以更新内容）
    if (
      content &&
      req.user.role !== 'admin' &&
      req.user.id !== existingMessage.userId
    ) {
      return error(res, 'Permission denied', 403);
    }

    // 准备更新数据
    const updateData = {};

    if (content) updateData.content = content;
    if (read !== undefined) updateData.read = read;

    // 更新消息
    const updatedMessage = await prisma.message.update({
      where: {id},
      data: updateData,
      include: {
        user: {
          select: {
            id: true,
            name: true,
            avatar: true
          }
        }
      }
    });

    return success(res, updatedMessage, 'Message updated successfully');
  } catch (err) {
    console.error('Update message error:', err);
    return error(res, 'Failed to update message', 500);
  }
};

/**
 * 删除消息
 * @route DELETE /api/messages/:id
 */
const deleteMessage = async (req, res) => {
  try {
    const {id} = req.params;

    // 检查消息是否存在
    const existingMessage = await prisma.message.findUnique({
      where: {id}
    });

    if (!existingMessage) {
      return error(res, 'Message not found', 404);
    }

    // 检查权限（只有管理员或消息所有者可以删除）
    if (req.user.role !== 'admin' && req.user.id !== existingMessage.userId) {
      return error(res, 'Permission denied', 403);
    }

    // 删除消息
    await prisma.message.delete({
      where: {id}
    });

    return success(res, null, 'Message deleted successfully');
  } catch (err) {
    console.error('Delete message error:', err);
    return error(res, 'Failed to delete message', 500);
  }
};

/**
 * 获取留言统计信息
 * @route GET /api/messages/statistics
 */
const getMessageStatistics = async (req, res) => {
  try {
    // 总留言数
    const totalMessages = await prisma.message.count();

    // 未读留言数
    const unreadMessages = await prisma.message.count({
      where: {read: false}
    });

    // 今日留言数
    const today = new Date();
    today.setHours(0, 0, 0, 0);
    const tomorrow = new Date(today);
    tomorrow.setDate(tomorrow.getDate() + 1);

    const todayMessages = await prisma.message.count({
      where: {
        createdAt: {
          gte: today,
          lt: tomorrow
        }
      }
    });

    // 最近留言
    const recentMessages = await prisma.message.findMany({
      take: 5,
      orderBy: {createdAt: 'desc'},
      include: {
        user: {
          select: {
            id: true,
            name: true,
            avatar: true
          }
        }
      }
    });

    return success(res, {
      totalMessages,
      unreadMessages,
      readMessages: totalMessages - unreadMessages,
      todayMessages,
      recentMessages
    });
  } catch (err) {
    console.error('Get message statistics error:', err);
    return error(res, 'Failed to get message statistics', 500);
  }
};

/**
 * 批量标记为已读
 * @route POST /api/messages/batch-read
 */
const batchMarkAsRead = async (req, res) => {
  try {
    const {messageIds} = req.body;

    if (!messageIds || !Array.isArray(messageIds)) {
      return error(res, 'Message IDs are required', 400);
    }

    const result = await prisma.message.updateMany({
      where: {id: {in: messageIds}},
      data: {read: true}
    });

    return success(res, result, 'Messages marked as read successfully');
  } catch (err) {
    console.error('Batch mark as read error:', err);
    return error(res, 'Failed to mark messages as read', 500);
  }
};

/**
 * 批量删除留言
 * @route POST /api/messages/batch-delete
 */
const batchDeleteMessages = async (req, res) => {
  try {
    const {messageIds} = req.body;

    if (!messageIds || !Array.isArray(messageIds)) {
      return error(res, 'Message IDs are required', 400);
    }

    const result = await prisma.message.deleteMany({
      where: {id: {in: messageIds}}
    });

    return success(res, result, 'Messages deleted successfully');
  } catch (err) {
    console.error('Batch delete messages error:', err);
    return error(res, 'Failed to delete messages', 500);
  }
};

module.exports = {
  getMessages,
  getMessageById,
  createMessage,
  updateMessage,
  deleteMessage,
  getMessageStatistics,
  batchMarkAsRead,
  batchDeleteMessages
};
