const {PrismaClient} = require('@prisma/client');
const {ROLES} = require('../middlewares/auth');

const prisma = new PrismaClient();

/**
 * 通知服务类
 * 负责智能推送通知给合适的用户
 */
class NotificationService {
  /**
   * 推送通知给指定用户
   * @param {string} userId 用户ID
   * @param {string} content 通知内容
   * @param {string} type 通知类型
   */
  async pushToUser(userId, content, type = 'general') {
    try {
      const notification = await prisma.notification.create({
        data: {
          content,
          userId,
          type,
          read: false
        },
        include: {
          user: {
            select: {id: true, name: true, role: true}
          }
        }
      });

      console.log(`通知已推送给用户 ${notification.user.name}: ${content}`);
      return notification;
    } catch (error) {
      console.error('推送通知失败:', error);
      throw error;
    }
  }

  /**
   * 推送通知给所有家庭成员
   * @param {string} content 通知内容
   * @param {string} type 通知类型
   * @param {string} excludeUserId 排除的用户ID（通常是发送者）
   */
  async pushToFamily(content, type = 'family', excludeUserId = null) {
    try {
      // 获取所有家庭成员（排除普通用户）
      const familyMembers = await prisma.user.findMany({
        where: {
          role: {
            in: [ROLES.ADMIN, ROLES.FAMILY_HEAD, ROLES.MEMBER]
          },
          ...(excludeUserId && {id: {not: excludeUserId}})
        },
        select: {id: true, name: true, role: true}
      });

      const notifications = await Promise.all(
        familyMembers.map(user =>
          prisma.notification.create({
            data: {
              content,
              userId: user.id,
              type,
              read: false
            }
          })
        )
      );

      console.log(
        `通知已推送给 ${familyMembers.length} 位家庭成员: ${content}`
      );
      return notifications;
    } catch (error) {
      console.error('推送家庭通知失败:', error);
      throw error;
    }
  }

  /**
   * 推送通知给管理员
   * @param {string} content 通知内容
   * @param {string} type 通知类型
   */
  async pushToAdmins(content, type = 'admin') {
    try {
      const admins = await prisma.user.findMany({
        where: {
          role: {
            in: [ROLES.ADMIN, ROLES.FAMILY_HEAD]
          }
        },
        select: {id: true, name: true, role: true}
      });

      const notifications = await Promise.all(
        admins.map(admin =>
          prisma.notification.create({
            data: {
              content,
              userId: admin.id,
              type,
              read: false
            }
          })
        )
      );

      console.log(`通知已推送给 ${admins.length} 位管理员: ${content}`);
      return notifications;
    } catch (error) {
      console.error('推送管理员通知失败:', error);
      throw error;
    }
  }

  /**
   * 推送通知给所有用户
   * @param {string} content 通知内容
   * @param {string} type 通知类型
   * @param {string} excludeUserId 排除的用户ID（通常是发送者）
   */
  async pushToAllUsers(content, type = 'general', excludeUserId = null) {
    try {
      // 获取所有用户（排除指定用户）
      const allUsers = await prisma.user.findMany({
        where: {
          ...(excludeUserId && {id: {not: excludeUserId}})
        },
        select: {id: true, name: true, role: true}
      });

      const notifications = await Promise.all(
        allUsers.map(user =>
          prisma.notification.create({
            data: {
              content,
              userId: user.id,
              type,
              read: false
            }
          })
        )
      );

      console.log(`通知已推送给 ${allUsers.length} 位用户: ${content}`);
      return notifications;
    } catch (error) {
      console.error('推送全体用户通知失败:', error);
      throw error;
    }
  }

  /**
   * 智能推送：新菜品添加通知
   * @param {object} dish 菜品信息
   * @param {object} creator 创建者信息
   */
  async notifyNewDish(dish, creator) {
    const content = `${creator.name} 添加了新菜品「${dish.name}」，快来看看吧！`;
    return this.pushToAllUsers(content, 'new_dish', creator.id);
  }

  /**
   * 智能推送：新订单通知
   * @param {object} order 订单信息
   * @param {object} customer 下单用户信息
   */
  async notifyNewOrder(order, customer) {
    const dishNames = order.items
      .map(item => item.dishName || item.name)
      .join('、');
    const content = `${customer.name} 下了新订单：${dishNames}`;

    // 推送给家庭管理员和管理员
    return this.pushToAdmins(content, 'new_order');
  }

  /**
   * 智能推送：菜单更新通知
   * @param {object} menu 菜单信息
   * @param {object} updater 更新者信息
   */
  async notifyMenuUpdate(menu, updater) {
    const content = `${updater.name} 更新了今日菜单，共 ${
      menu.items?.length || 0
    } 道菜`;
    return this.pushToFamily(content, 'menu_update', updater.id);
  }

  /**
   * 智能推送：家庭消息通知
   * @param {object} message 消息信息
   * @param {object} sender 发送者信息
   */
  async notifyFamilyMessage(message, sender) {
    const content = `${sender.name} 发布了新消息：${message.content.substring(
      0,
      20
    )}${message.content.length > 20 ? '...' : ''}`;
    return this.pushToFamily(content, 'family_message', sender.id);
  }

  /**
   * 智能推送：用户注册通知
   * @param {object} newUser 新用户信息
   */
  async notifyUserRegistration(newUser) {
    const content = `新用户 ${newUser.name} 已注册，手机号：${newUser.phone}`;
    return this.pushToAdmins(content, 'user_registration');
  }

  /**
   * 智能推送：系统维护通知
   * @param {string} content 维护内容
   */
  async notifySystemMaintenance(content) {
    // 推送给所有用户
    const allUsers = await prisma.user.findMany({
      select: {id: true, name: true}
    });

    const notifications = await Promise.all(
      allUsers.map(user =>
        prisma.notification.create({
          data: {
            content,
            userId: user.id,
            type: 'system',
            read: false
          }
        })
      )
    );

    console.log(`系统通知已推送给 ${allUsers.length} 位用户: ${content}`);
    return notifications;
  }

  /**
   * 获取用户未读通知数量
   * @param {string} userId 用户ID
   */
  async getUnreadCount(userId) {
    return prisma.notification.count({
      where: {
        userId,
        read: false
      }
    });
  }

  /**
   * 标记通知为已读
   * @param {string} notificationId 通知ID
   * @param {string} userId 用户ID
   */
  async markAsRead(notificationId, userId) {
    return prisma.notification.updateMany({
      where: {
        id: notificationId,
        userId
      },
      data: {
        read: true
      }
    });
  }

  /**
   * 标记用户所有通知为已读
   * @param {string} userId 用户ID
   */
  async markAllAsRead(userId) {
    return prisma.notification.updateMany({
      where: {
        userId,
        read: false
      },
      data: {
        read: true
      }
    });
  }

  /**
   * 清理过期通知（30天前的已读通知）
   */
  async cleanupOldNotifications() {
    const thirtyDaysAgo = new Date();
    thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30);

    const result = await prisma.notification.deleteMany({
      where: {
        read: true,
        createdAt: {
          lt: thirtyDaysAgo
        }
      }
    });

    console.log(`清理了 ${result.count} 条过期通知`);
    return result;
  }
}

module.exports = new NotificationService();
