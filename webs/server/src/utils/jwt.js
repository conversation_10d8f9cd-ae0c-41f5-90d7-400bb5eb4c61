const jwt = require('jsonwebtoken');
const crypto = require('crypto');

// JWT 配置
const JWT_SECRET =
  process.env.JWT_SECRET || 'your-super-secret-jwt-key-change-in-production';
const JWT_EXPIRES_IN = process.env.JWT_EXPIRES_IN || '7d';
const REFRESH_TOKEN_EXPIRES_IN = process.env.REFRESH_TOKEN_EXPIRES_IN || '30d';

// 存储活跃的 token（生产环境应使用 Redis）
const activeTokens = new Map();
const refreshTokens = new Map();

/**
 * 生成 JWT token（保持向后兼容）
 * @param {Object} payload - 要编码到 token 中的数据
 * @returns {string} JWT token
 */
const generateToken = payload => {
  return jwt.sign(payload, JWT_SECRET, {expiresIn: JWT_EXPIRES_IN});
};

/**
 * 验证 JWT token（保持向后兼容）
 * @param {string} token - JWT token
 * @returns {Object|null} 解码后的 payload 或 null（如果验证失败）
 */
const verifyToken = token => {
  try {
    return jwt.verify(token, JWT_SECRET);
  } catch (error) {
    return null;
  }
};

/**
 * 生成访问令牌（单点登录版本）
 * @param {Object} payload 用户信息
 * @returns {string} JWT token
 */
function generateAccessToken(payload) {
  const token = jwt.sign(payload, JWT_SECRET, {
    expiresIn: JWT_EXPIRES_IN,
    issuer: 'nannan-kitchen',
    audience: 'nannan-kitchen-users'
  });

  // 记录活跃 token
  activeTokens.set(token, {
    userId: payload.id,
    createdAt: new Date(),
    lastUsed: new Date()
  });

  return token;
}

/**
 * 生成刷新令牌
 * @param {string} userId 用户ID
 * @returns {string} refresh token
 */
function generateRefreshToken(userId) {
  const refreshToken = crypto.randomBytes(64).toString('hex');

  // 存储刷新令牌
  refreshTokens.set(refreshToken, {
    userId,
    createdAt: new Date(),
    expiresAt: new Date(Date.now() + parseDuration(REFRESH_TOKEN_EXPIRES_IN))
  });

  return refreshToken;
}

/**
 * 验证访问令牌（单点登录版本）
 * @param {string} token JWT token
 * @returns {Object|null} 解码后的用户信息
 */
function verifyAccessToken(token) {
  try {
    const decoded = jwt.verify(token, JWT_SECRET);

    // 检查 token 是否在活跃列表中
    if (!activeTokens.has(token)) {
      throw new Error('Token has been revoked');
    }

    // 更新最后使用时间
    const tokenInfo = activeTokens.get(token);
    tokenInfo.lastUsed = new Date();
    activeTokens.set(token, tokenInfo);

    return decoded;
  } catch (error) {
    console.error('Token verification failed:', error.message);
    return null;
  }
}

/**
 * 撤销用户的所有令牌（单点登录：踢出其他设备）
 * @param {string} userId 用户ID
 */
function revokeUserTokens(userId) {
  // 撤销所有访问令牌
  for (const [token, tokenInfo] of activeTokens.entries()) {
    if (tokenInfo.userId === userId) {
      activeTokens.delete(token);
    }
  }

  // 撤销所有刷新令牌
  for (const [refreshToken, tokenInfo] of refreshTokens.entries()) {
    if (tokenInfo.userId === userId) {
      refreshTokens.delete(refreshToken);
    }
  }
}

/**
 * 生成完整的令牌对
 * @param {Object} user 用户信息
 * @param {boolean} singleSession 是否单点登录
 * @returns {Object} 包含访问令牌和刷新令牌的对象
 */
function generateTokenPair(user, singleSession = true) {
  // 如果启用单点登录，撤销用户的所有现有令牌
  if (singleSession) {
    revokeUserTokens(user.id);
  }

  const payload = {
    id: user.id,
    name: user.name,
    phone: user.phone,
    role: user.role
  };

  const accessToken = generateAccessToken(payload);
  const refreshToken = generateRefreshToken(user.id);

  return {
    accessToken,
    refreshToken,
    expiresIn: JWT_EXPIRES_IN,
    tokenType: 'Bearer'
  };
}

/**
 * 解析时间字符串为毫秒
 * @param {string} duration 时间字符串，如 '7d', '24h', '30m'
 * @returns {number} 毫秒数
 */
function parseDuration(duration) {
  const units = {
    s: 1000,
    m: 60 * 1000,
    h: 60 * 60 * 1000,
    d: 24 * 60 * 60 * 1000
  };

  const match = duration.match(/^(\d+)([smhd])$/);
  if (!match) {
    throw new Error('Invalid duration format');
  }

  const [, value, unit] = match;
  return parseInt(value) * units[unit];
}

module.exports = {
  // 向后兼容的方法
  generateToken,
  verifyToken,

  // 新的单点登录方法
  generateAccessToken,
  generateRefreshToken,
  verifyAccessToken,
  revokeUserTokens,
  generateTokenPair
};
