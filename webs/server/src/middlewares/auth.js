const {verifyToken, verifyAccessToken} = require('../utils/jwt');
const {error} = require('../utils/response');
const {PrismaClient} = require('@prisma/client');

const prisma = new PrismaClient();

// 角色权限定义
const ROLES = {
  ADMIN: 'admin',
  FAMILY_HEAD: 'family_head', // 家庭管理员
  MEMBER: 'member', // 家庭成员
  USER: 'user' // 普通用户
};

// 权限级别定义
const PERMISSIONS = {
  // 用户管理
  MANAGE_USERS: 'manage_users',
  VIEW_USERS: 'view_users',

  // 菜品管理
  MANAGE_DISHES: 'manage_dishes',
  CREATE_DISHES: 'create_dishes',
  VIEW_DISHES: 'view_dishes',

  // 菜单管理
  MANAGE_MENUS: 'manage_menus',
  CREATE_MENUS: 'create_menus',
  VIEW_MENUS: 'view_menus',

  // 订单管理
  MANAGE_ORDERS: 'manage_orders',
  CREATE_ORDERS: 'create_orders',
  VIEW_ORDERS: 'view_orders',

  // 消息管理
  MANAGE_MESSAGES: 'manage_messages',
  CREATE_MESSAGES: 'create_messages',
  VIEW_MESSAGES: 'view_messages',

  // 通知管理
  MANAGE_NOTIFICATIONS: 'manage_notifications',
  CREATE_NOTIFICATIONS: 'create_notifications',
  VIEW_NOTIFICATIONS: 'view_notifications'
};

// 角色权限映射
const ROLE_PERMISSIONS = {
  [ROLES.ADMIN]: [
    // 管理员拥有所有权限
    ...Object.values(PERMISSIONS)
  ],
  [ROLES.FAMILY_HEAD]: [
    // 家庭管理员权限
    PERMISSIONS.VIEW_USERS,
    PERMISSIONS.MANAGE_DISHES,
    PERMISSIONS.CREATE_DISHES,
    PERMISSIONS.VIEW_DISHES,
    PERMISSIONS.MANAGE_MENUS,
    PERMISSIONS.CREATE_MENUS,
    PERMISSIONS.VIEW_MENUS,
    PERMISSIONS.MANAGE_ORDERS,
    PERMISSIONS.CREATE_ORDERS,
    PERMISSIONS.VIEW_ORDERS,
    PERMISSIONS.MANAGE_MESSAGES,
    PERMISSIONS.CREATE_MESSAGES,
    PERMISSIONS.VIEW_MESSAGES,
    PERMISSIONS.MANAGE_NOTIFICATIONS,
    PERMISSIONS.CREATE_NOTIFICATIONS,
    PERMISSIONS.VIEW_NOTIFICATIONS
  ],
  [ROLES.MEMBER]: [
    // 家庭成员权限
    PERMISSIONS.CREATE_DISHES,
    PERMISSIONS.VIEW_DISHES,
    PERMISSIONS.CREATE_MENUS,
    PERMISSIONS.VIEW_MENUS,
    PERMISSIONS.CREATE_ORDERS,
    PERMISSIONS.VIEW_ORDERS,
    PERMISSIONS.CREATE_MESSAGES,
    PERMISSIONS.VIEW_MESSAGES,
    PERMISSIONS.VIEW_NOTIFICATIONS
  ],
  [ROLES.USER]: [
    // 普通用户权限
    PERMISSIONS.VIEW_DISHES,
    PERMISSIONS.VIEW_MENUS,
    PERMISSIONS.CREATE_ORDERS,
    PERMISSIONS.VIEW_ORDERS,
    PERMISSIONS.VIEW_MESSAGES,
    PERMISSIONS.VIEW_NOTIFICATIONS
  ]
};

/**
 * 认证中间件
 * 验证请求头中的 JWT token
 */
const auth = async (req, res, next) => {
  try {
    // 获取 Authorization 头
    const authHeader = req.headers.authorization;

    if (!authHeader || !authHeader.startsWith('Bearer ')) {
      return error(res, 'Authentication required', 401);
    }

    // 提取 token
    const token = authHeader.split(' ')[1];

    // 验证 token (支持新旧两种方式)
    let decoded = verifyAccessToken(token); // 先尝试新的单点登录验证

    if (!decoded) {
      decoded = verifyToken(token); // 降级到旧的验证方式
    }

    if (!decoded) {
      return error(res, 'Invalid or expired token', 401);
    }

    // 从数据库获取最新的用户信息
    const user = await prisma.user.findUnique({
      where: {id: decoded.id},
      select: {id: true, name: true, phone: true, role: true, avatar: true}
    });

    if (!user) {
      return error(res, 'User not found', 401);
    }

    // 将用户信息添加到请求对象
    req.user = user;

    next();
  } catch (err) {
    console.error('Authentication error:', err);
    return error(res, 'Authentication failed', 401);
  }
};

/**
 * 权限检查中间件工厂
 * @param {string} permission 需要的权限
 */
const requirePermission = permission => {
  return (req, res, next) => {
    if (!req.user) {
      return error(res, 'Authentication required', 401);
    }

    const userRole = req.user.role;
    const userPermissions = ROLE_PERMISSIONS[userRole] || [];

    if (!userPermissions.includes(permission)) {
      return error(res, `Permission denied. Required: ${permission}`, 403);
    }

    next();
  };
};

/**
 * 管理员权限中间件
 */
const adminAuth = (req, res, next) => {
  if (!req.user || req.user.role !== ROLES.ADMIN) {
    return error(res, 'Admin privileges required', 403);
  }

  next();
};

/**
 * 家庭管理员权限中间件
 */
const familyHeadAuth = (req, res, next) => {
  if (!req.user || ![ROLES.ADMIN, ROLES.FAMILY_HEAD].includes(req.user.role)) {
    return error(res, 'Family head privileges required', 403);
  }

  next();
};

/**
 * 检查用户是否有权限访问特定资源
 * @param {string} userId 资源所有者ID
 * @param {object} currentUser 当前用户
 */
const canAccessUserResource = (userId, currentUser) => {
  // 管理员和家庭管理员可以访问所有资源
  if ([ROLES.ADMIN, ROLES.FAMILY_HEAD].includes(currentUser.role)) {
    return true;
  }

  // 用户只能访问自己的资源
  return userId === currentUser.id;
};

module.exports = {
  auth,
  adminAuth,
  familyHeadAuth,
  requirePermission,
  canAccessUserResource,
  ROLES,
  PERMISSIONS,
  ROLE_PERMISSIONS
};
