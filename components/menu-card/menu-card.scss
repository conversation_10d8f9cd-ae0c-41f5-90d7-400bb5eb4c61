.menu-card {
  width: 112rpx;
  min-width: 112rpx;
  max-width: 120rpx;
  border-radius: 20rpx;
  box-shadow: 0 4rpx 16rpx #0004;
  display: flex;
  flex-direction: column;
  align-items: center;
  position: relative;
  border: 2rpx solid #232323;
  background: linear-gradient(135deg, #23272f 60%, #181a20 100%);
  padding: 16rpx 8rpx 12rpx 8rpx;
  margin-right: 16rpx;
  transition: box-shadow 0.18s;
  box-sizing: border-box;
  height: 148rpx;
  justify-content: flex-start;

  .food-img {
    width: 56rpx;
    height: 56rpx;
    border-radius: 50%;
    object-fit: cover;
    border: 4rpx solid #00f2ea;
    margin-bottom: 8rpx;
    background: #181a20;
    box-shadow: 0 2rpx 8rpx #0004;
  }
  .food-name {
    color: #fff;
    font-weight: 600;
    font-size: 26rpx;
    margin-bottom: 6rpx;
    letter-spacing: 1rpx;
    text-align: center;
    line-height: 32rpx;
  }
  .food-count {
    background: #232323;
    color: #fe2c55;
    font-size: 18rpx;
    font-weight: bold;
    border-radius: 999rpx;
    padding: 0 12rpx;
    position: absolute;
    top: 12rpx;
    right: 12rpx;
    box-shadow: 0 2rpx 8rpx #0004;
    line-height: 28rpx;
    height: 28rpx;
    min-width: 40rpx;
    text-align: center;
  }
}
