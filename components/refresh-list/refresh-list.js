Component({
  properties: {
    // 列表高度
    height: {
      type: String,
      value: '100vh'
    },
    
    // 是否启用下拉刷新
    refresherEnabled: {
      type: Boolean,
      value: true
    },
    
    // 下拉刷新阈值
    refresherThreshold: {
      type: Number,
      value: 45
    },
    
    // 下拉刷新默认样式
    refresherDefaultStyle: {
      type: String,
      value: 'none'
    },
    
    // 下拉刷新背景色
    refresherBackground: {
      type: String,
      value: '#f5f5f5'
    },
    
    // 上拉加载阈值
    lowerThreshold: {
      type: Number,
      value: 50
    },
    
    // 是否显示自定义下拉刷新
    showCustomRefresher: {
      type: Boolean,
      value: true
    },
    
    // 是否显示加载更多
    showLoadMore: {
      type: Boolean,
      value: true
    },
    
    // 加载更多状态: loading, nomore, error, none
    loadMoreStatus: {
      type: String,
      value: 'none'
    },
    
    // 是否为空
    isEmpty: {
      type: Boolean,
      value: false
    },
    
    // 是否正在加载
    loading: {
      type: Boolean,
      value: false
    },
    
    // 文本配置
    loadingText: {
      type: String,
      value: '加载中...'
    },
    
    noMoreText: {
      type: String,
      value: '没有更多数据了'
    },
    
    errorText: {
      type: String,
      value: '加载失败，点击重试'
    },
    
    emptyText: {
      type: String,
      value: '暂无数据'
    },
    
    emptyImage: {
      type: String,
      value: '/assets/image/empty_cart.svg'
    },
    
    showEmptyAction: {
      type: Boolean,
      value: false
    },
    
    emptyActionText: {
      type: String,
      value: '重新加载'
    }
  },

  data: {
    refresherTriggered: false,
    refresherText: '下拉刷新'
  },

  methods: {
    // 下拉刷新
    onRefresh() {
      this.setData({
        refresherTriggered: true,
        refresherText: '正在刷新...'
      });

      // 触发父组件的刷新事件
      this.triggerEvent('refresh', {}, {
        bubbles: true,
        composed: true
      });
    },

    // 停止下拉刷新
    stopRefresh() {
      this.setData({
        refresherTriggered: false,
        refresherText: '下拉刷新'
      });
    },

    // 上拉加载更多
    onLoadMore() {
      // 如果正在加载或没有更多数据，则不触发
      if (this.data.loadMoreStatus === 'loading' || 
          this.data.loadMoreStatus === 'nomore') {
        return;
      }

      // 触发父组件的加载更多事件
      this.triggerEvent('loadmore', {}, {
        bubbles: true,
        composed: true
      });
    },

    // 空状态操作
    onEmptyAction() {
      this.triggerEvent('emptyaction', {}, {
        bubbles: true,
        composed: true
      });
    },

    // 设置加载更多状态
    setLoadMoreStatus(status) {
      this.setData({
        loadMoreStatus: status
      });
    },

    // 重置状态
    reset() {
      this.setData({
        refresherTriggered: false,
        refresherText: '下拉刷新',
        loadMoreStatus: 'none'
      });
    }
  },

  observers: {
    'refresherTriggered': function(triggered) {
      if (!triggered) {
        this.setData({
          refresherText: '下拉刷新'
        });
      }
    }
  },

  lifetimes: {
    attached() {
      // 组件初始化
    },

    detached() {
      // 组件销毁
    }
  }
});
