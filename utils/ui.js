/**
 * UI 交互工具类
 * 统一管理 Toast、Loading、Dialog 等交互提示
 */

import Toast from '@vant/weapp/lib/toast/toast';
import Dialog from '@vant/weapp/lib/dialog/dialog';
import Notify from '@vant/weapp/lib/notify/notify';

class UIManager {
  constructor() {
    this.loadingCount = 0; // 防止多个loading同时显示
  }

  /**
   * 显示成功提示
   * @param {string} message 提示信息
   * @param {number} duration 持续时间，默认2000ms
   */
  showSuccess(message, duration = 2000) {
    Toast.success({
      message,
      duration,
      forbidClick: true
    });
  }

  /**
   * 显示失败提示
   * @param {string} message 提示信息
   * @param {number} duration 持续时间，默认2000ms
   */
  showError(message, duration = 2000) {
    Toast.fail({
      message,
      duration,
      forbidClick: true
    });
  }

  /**
   * 显示普通提示
   * @param {string} message 提示信息
   * @param {number} duration 持续时间，默认2000ms
   */
  showToast(message, duration = 2000) {
    Toast({
      message,
      duration,
      forbidClick: true
    });
  }

  /**
   * 显示加载中
   * @param {string} message 加载提示文字，默认"加载中..."
   */
  showLoading(message = '加载中...') {
    this.loadingCount++;
    Toast.loading({
      message,
      forbidClick: true,
      duration: 0 // 不自动关闭
    });
  }

  /**
   * 隐藏加载中
   */
  hideLoading() {
    this.loadingCount--;
    if (this.loadingCount <= 0) {
      this.loadingCount = 0;
      Toast.clear();
    }
  }

  /**
   * 显示确认对话框
   * @param {Object} options 配置选项
   * @returns {Promise}
   */
  showConfirm(options = {}) {
    const defaultOptions = {
      title: '提示',
      message: '确定要执行此操作吗？',
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      confirmButtonColor: '#3b82f6',
      cancelButtonColor: '#6b7280'
    };

    return Dialog.confirm({
      ...defaultOptions,
      ...options
    });
  }

  /**
   * 显示警告对话框
   * @param {Object} options 配置选项
   * @returns {Promise}
   */
  showAlert(options = {}) {
    const defaultOptions = {
      title: '提示',
      confirmButtonText: '确定',
      confirmButtonColor: '#3b82f6'
    };

    return Dialog.alert({
      ...defaultOptions,
      ...options
    });
  }

  /**
   * 显示顶部通知
   * @param {Object} options 配置选项
   */
  showNotify(options = {}) {
    const defaultOptions = {
      type: 'primary',
      duration: 3000,
      safeAreaInsetTop: true
    };

    Notify({
      ...defaultOptions,
      ...options
    });
  }

  /**
   * 显示成功通知
   * @param {string} message 通知信息
   */
  showSuccessNotify(message) {
    this.showNotify({
      type: 'success',
      message
    });
  }

  /**
   * 显示错误通知
   * @param {string} message 通知信息
   */
  showErrorNotify(message) {
    this.showNotify({
      type: 'danger',
      message
    });
  }

  /**
   * 显示警告通知
   * @param {string} message 通知信息
   */
  showWarningNotify(message) {
    this.showNotify({
      type: 'warning',
      message
    });
  }

  /**
   * 清除所有提示
   */
  clearAll() {
    Toast.clear();
    Dialog.close();
    Notify.clear();
    this.loadingCount = 0;
  }

  /**
   * 网络请求包装器
   * @param {Function} requestFn 请求函数
   * @param {Object} options 配置选项
   */
  async withLoading(requestFn, options = {}) {
    const {
      loadingText = '加载中...',
      successText,
      errorText = '操作失败',
      showSuccess = false,
      showError = true
    } = options;

    try {
      this.showLoading(loadingText);
      const result = await requestFn();

      if (showSuccess && successText) {
        this.showSuccess(successText);
      }

      return result;
    } catch (error) {
      console.error('Request error:', error);

      if (showError) {
        const message = error.message || errorText;
        this.showError(message);
      }

      throw error;
    } finally {
      this.hideLoading();
    }
  }

  /**
   * 防抖执行函数
   * @param {Function} fn 要执行的函数
   * @param {number} delay 延迟时间，默认300ms
   */
  debounce(fn, delay = 300) {
    let timer = null;
    return function (...args) {
      if (timer) clearTimeout(timer);
      timer = setTimeout(() => {
        fn.apply(this, args);
      }, delay);
    };
  }

  /**
   * 节流执行函数
   * @param {Function} fn 要执行的函数
   * @param {number} delay 延迟时间，默认300ms
   */
  throttle(fn, delay = 300) {
    let timer = null;
    return function (...args) {
      if (timer) return;
      timer = setTimeout(() => {
        fn.apply(this, args);
        timer = null;
      }, delay);
    };
  }
}

// 创建全局实例
const ui = new UIManager();

// 挂载到全局 app
const app = getApp();
if (app) {
  app.ui = ui;
}

export default ui;
