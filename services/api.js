/**
 * API 服务类
 * 封装所有 API 请求
 */

const {get, post, put, del} = require('../utils/request');

/**
 * 用户相关 API
 */
const userApi = {
  // 登录
  login: data => post('/auth/login', data),

  // 注册
  register: data => post('/auth/register', data),

  // 获取用户信息
  getUserInfo: id => get(`/users/${id}`),

  // 获取家庭成员列表
  getFamilyMembers: () => get('/users/family'),

  // 更新用户信息
  updateUserInfo: (id, data) => put(`/users/${id}`, data)
};

/**
 * 菜单相关 API
 */
const menuApi = {
  // 获取菜品类别
  getCategories: () => get('/menus/categories'),

  // 获取所有菜品（按分类）
  getDishes: () => get('/dishes/by-category'),

  // 获取今日菜单
  getTodayMenu: () => get('/menus/today'),

  // 获取历史菜单
  getHistoryMenus: () => get('/menus/history'),

  // 获取推荐菜单
  getRecommendedMenu: () => get('/menus/recommended'),

  // 获取统计信息
  getStatistics: () => get('/menus/statistics'),

  // 创建菜单
  createMenu: data => post('/menus', data),

  // 更新菜单
  updateMenu: (id, data) => put(`/menus/${id}`, data),

  // 更新菜单中的菜品
  updateMenuDishes: (id, data) => put(`/menus/${id}/dishes`, data)
};

/**
 * 订单相关 API
 */
const orderApi = {
  // 获取所有订单
  getOrders: () => get('/orders'),

  // 获取今日订单
  getTodayOrders: () => get('/orders/today'),

  // 获取指定订单
  getOrder: id => get(`/orders/${id}`),

  // 创建订单
  createOrder: data => post('/orders', data),

  // 更新订单
  updateOrder: (id, data) => put(`/orders/${id}`, data),

  // 删除订单
  deleteOrder: id => del(`/orders/${id}`)
};

/**
 * 消息相关 API
 */
const messageApi = {
  // 获取所有消息（支持分页和排序）
  getMessages: (params = {}) => {
    const queryString =
      Object.keys(params).length > 0
        ? '?' +
          Object.keys(params)
            .map(key => `${key}=${params[key]}`)
            .join('&')
        : '';
    return get(`/messages${queryString}`);
  },

  // 获取指定消息
  getMessage: id => get(`/messages/${id}`),

  // 创建消息
  createMessage: data => post('/messages', data),

  // 更新消息
  updateMessage: (id, data) => put(`/messages/${id}`, data),

  // 删除消息
  deleteMessage: id => del(`/messages/${id}`)
};

/**
 * 通知相关 API
 */
const notificationApi = {
  // 获取所有通知（支持分页和筛选）
  getNotifications: (params = {}) => {
    const queryString =
      Object.keys(params).length > 0
        ? '?' +
          Object.keys(params)
            .map(key => `${key}=${params[key]}`)
            .join('&')
        : '';
    return get(`/notifications${queryString}`);
  },

  // 获取指定通知
  getNotification: id => get(`/notifications/${id}`),

  // 创建通知
  createNotification: data => post('/notifications', data),

  // 更新通知
  updateNotification: (id, data) => put(`/notifications/${id}`, data),

  // 标记通知为已读
  markAsRead: id => put(`/notifications/${id}/read`, {}),

  // 全部标记为已读
  markAllRead: () => put('/notifications/read-all', {}),

  // 删除通知
  deleteNotification: id => del(`/notifications/${id}`)
};

/**
 * 菜品相关 API
 */
const dishApi = {
  // 获取菜品详情
  getDishDetail: id => get(`/dishes/${id}/detail`),

  // 获取所有菜品
  getDishes: () => get('/dishes'),

  // 获取分类菜品
  getDishesByCategory: () => get('/dishes/by-category'),

  // 创建菜品
  createDish: data => post('/dishes', data)
};

module.exports = {
  userApi,
  menuApi,
  orderApi,
  messageApi,
  notificationApi,
  dishApi
};
